#!/usr/bin/env python3
"""
Test script to verify that the labor loading endpoint now uses work order site ID
instead of user site ID for filtering.
"""

import sys
import os
import re
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_labor_endpoint_uses_workorder_site_id():
    """
    Test that the /api/task/<task_wonum>/labor endpoint now uses work order site ID.
    """
    print("🧪 Testing Labor Endpoint Site ID Fix")
    print("=" * 50)
    
    try:
        # Read the app.py file to verify changes
        with open('app.py', 'r') as f:
            app_content = f.read()
        
        # Find the labor endpoint function
        labor_endpoint_start = app_content.find('@app.route(\'/api/task/<task_wonum>/labor\'')
        if labor_endpoint_start == -1:
            print("❌ Labor endpoint not found")
            return False
            
        # Find the end of the function (next @app.route or end of file)
        labor_endpoint_end = app_content.find('@app.route', labor_endpoint_start + 1)
        if labor_endpoint_end == -1:
            labor_endpoint_end = len(app_content)
        
        labor_endpoint_code = app_content[labor_endpoint_start:labor_endpoint_end]
        
        # Check for the new work order site ID logic
        checks = [
            {
                'name': 'Uses task_labor_service.get_workorder_site_id()',
                'pattern': r'workorder_site_id\s*=\s*task_labor_service\.get_workorder_site_id\(task_wonum\)',
                'required': True
            },
            {
                'name': 'Uses workorder_site_id in filter',
                'pattern': r'workorder_site_id.*!=.*"UNKNOWN"',
                'required': True
            },
            {
                'name': 'Logs work order site ID',
                'pattern': r'work order site ID.*workorder_site_id',
                'required': True
            },
            {
                'name': 'Does not use user_site_id variable',
                'pattern': r'user_site_id\s*=',
                'required': False  # Should NOT be present
            },
            {
                'name': 'Does not get user profile for site ID',
                'pattern': r'enhanced_profile_service\.get_user_profile',
                'required': False  # Should NOT be present
            }
        ]
        
        results = []
        for check in checks:
            found = bool(re.search(check['pattern'], labor_endpoint_code, re.IGNORECASE))
            
            if check['required']:
                # This pattern should be present
                if found:
                    print(f"✅ {check['name']}")
                    results.append(True)
                else:
                    print(f"❌ {check['name']} - NOT FOUND")
                    results.append(False)
            else:
                # This pattern should NOT be present
                if not found:
                    print(f"✅ {check['name']} - correctly removed")
                    results.append(True)
                else:
                    print(f"⚠️  {check['name']} - still present (may need cleanup)")
                    results.append(False)
        
        # Check that TaskLaborService is properly imported and initialized
        if 'task_labor_service = TaskLaborService(token_manager)' in app_content:
            print("✅ TaskLaborService is properly initialized")
            results.append(True)
        else:
            print("❌ TaskLaborService not properly initialized")
            results.append(False)
        
        # Summary
        passed_checks = sum(results)
        total_checks = len(results)
        
        print(f"\n📊 Results: {passed_checks}/{total_checks} checks passed")
        
        if passed_checks == total_checks:
            print("✅ Labor endpoint successfully updated to use work order site ID")
            return True
        else:
            print("❌ Some checks failed - labor endpoint may not be fully updated")
            return False
            
    except FileNotFoundError:
        print("❌ app.py file not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing app.py: {e}")
        return False

def test_frontend_calls_correct_endpoint():
    """
    Test that the frontend is calling the labor endpoint that we fixed.
    """
    print("\n🌐 Testing Frontend Endpoint Usage")
    print("=" * 50)
    
    try:
        # Check workorder_detail.html for the endpoint being called
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Look for the fetch call to the labor endpoint
        if '/api/task/${taskWonum}/labor' in template_content:
            print("✅ Frontend calls /api/task/<task_wonum>/labor endpoint")
            print("✅ This is the endpoint we fixed to use work order site ID")
            return True
        elif '/api/task/${taskWonum}/labor-records' in template_content:
            print("ℹ️  Frontend calls /api/task/<task_wonum>/labor-records endpoint")
            print("ℹ️  This endpoint already uses work order site ID")
            return True
        else:
            print("❌ Could not find labor endpoint call in frontend")
            return False
            
    except FileNotFoundError:
        print("❌ workorder_detail.html file not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing frontend: {e}")
        return False

def main():
    """Main test function"""
    print(f"🚀 Labor Site ID Fix Verification")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Run tests
    endpoint_test_passed = test_labor_endpoint_uses_workorder_site_id()
    frontend_test_passed = test_frontend_calls_correct_endpoint()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS:")
    
    if endpoint_test_passed and frontend_test_passed:
        print("✅ ALL TESTS PASSED!")
        print("✅ Labor loading now uses work order site ID instead of user site ID")
        print("✅ Frontend calls the correct (fixed) endpoint")
        print("✅ Labor filtering should now work consistently with materials")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        if not endpoint_test_passed:
            print("❌ Labor endpoint fix verification failed")
        if not frontend_test_passed:
            print("❌ Frontend endpoint usage verification failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
