#!/usr/bin/env python3
"""
Test script for the editable URL feature.
This script tests the URL functionality without requiring a full login.
"""

import os
import sys
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_update_env_file():
    """Test the update_env_file function."""
    print("🧪 Testing update_env_file function...")
    
    # Create a temporary .env file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as temp_file:
        temp_file.write("""# Test .env file
MAXIMO_BASE_URL=https://old-url.com/maximo
MAXIMO_API_KEY=test_key
OTHER_VAR=test_value
""")
        temp_env_path = temp_file.name
    
    try:
        # Import the function (this will also load the real .env)
        from app import update_env_file
        
        # Temporarily replace the .env path
        original_cwd = os.getcwd()
        temp_dir = os.path.dirname(temp_env_path)
        temp_filename = os.path.basename(temp_env_path)
        
        # Copy temp file to current directory as .env
        test_env_path = os.path.join(original_cwd, '.env.test')
        shutil.copy2(temp_env_path, test_env_path)
        
        # Mock the .env file path in the function
        with patch('app.env_file_path', test_env_path):
            # Test updating existing key
            update_env_file('MAXIMO_BASE_URL', 'https://new-url.com/maximo')
            
            # Read and verify the file
            with open(test_env_path, 'r') as f:
                content = f.read()
                
            print("✅ Updated .env file content:")
            print(content)
            
            # Verify the URL was updated
            assert 'MAXIMO_BASE_URL=https://new-url.com/maximo' in content
            assert 'MAXIMO_API_KEY=test_key' in content  # Other values preserved
            assert 'OTHER_VAR=test_value' in content
            
            print("✅ update_env_file test passed!")
            
    except Exception as e:
        print(f"❌ update_env_file test failed: {e}")
        return False
    finally:
        # Clean up
        for path in [temp_env_path, test_env_path]:
            if os.path.exists(path):
                os.unlink(path)
    
    return True

def test_url_validation():
    """Test URL validation logic."""
    print("\n🧪 Testing URL validation...")
    
    test_cases = [
        ("https://valid.com/maximo", True),
        ("http://valid.com/maximo", True),
        ("https://test.maximo.com", True),
        ("ftp://invalid.com", False),
        ("invalid-url", False),
        ("", False),
        ("javascript:alert('xss')", False),
    ]
    
    for url, expected_valid in test_cases:
        # Simple validation logic (same as in the app)
        is_valid = url.startswith(('http://', 'https://')) and len(url.strip()) > 0
        
        if is_valid == expected_valid:
            print(f"✅ URL '{url}' validation: {'VALID' if is_valid else 'INVALID'}")
        else:
            print(f"❌ URL '{url}' validation failed: expected {'VALID' if expected_valid else 'INVALID'}, got {'VALID' if is_valid else 'INVALID'}")
            return False
    
    print("✅ URL validation tests passed!")
    return True

def test_environment_loading():
    """Test that environment variables are loaded correctly."""
    print("\n🧪 Testing environment loading...")
    
    try:
        # Import after setting up the environment
        from app import DEFAULT_MAXIMO_URL
        
        print(f"✅ DEFAULT_MAXIMO_URL loaded: {DEFAULT_MAXIMO_URL}")
        
        # Verify it's a valid URL
        if DEFAULT_MAXIMO_URL.startswith(('http://', 'https://')):
            print("✅ Default URL format is valid")
            return True
        else:
            print(f"❌ Default URL format is invalid: {DEFAULT_MAXIMO_URL}")
            return False
            
    except Exception as e:
        print(f"❌ Environment loading test failed: {e}")
        return False

def test_flask_app_creation():
    """Test that the Flask app can be created with the new URL feature."""
    print("\n🧪 Testing Flask app creation...")
    
    try:
        # Mock the token manager to avoid actual connections
        with patch('app.MaximoTokenManager') as mock_token_manager:
            mock_token_manager.return_value = MagicMock()
            
            # Import the app
            from app import app, DEFAULT_MAXIMO_URL
            
            # Test that app was created
            assert app is not None
            print("✅ Flask app created successfully")
            
            # Test that token manager was initialized with URL
            mock_token_manager.assert_called_with(DEFAULT_MAXIMO_URL)
            print("✅ Token manager initialized with correct URL")
            
            return True
            
    except Exception as e:
        print(f"❌ Flask app creation test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Editable URL Feature Tests")
    print("=" * 50)
    
    tests = [
        test_url_validation,
        test_environment_loading,
        test_flask_app_creation,
        test_update_env_file,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The editable URL feature is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
