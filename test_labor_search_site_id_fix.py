#!/usr/bin/env python3
"""
Test script to verify that the labor search now uses work order site ID
instead of user site ID for filtering.
"""

import sys
import os
import re
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_labor_search_uses_workorder_site_id():
    """
    Test that the labor search button now uses work order/task site ID.
    """
    print("🧪 Testing Labor Search Site ID Fix")
    print("=" * 50)
    
    try:
        # Read the workorder_detail.html template
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Find the labor search button
        labor_search_pattern = r'openLaborSearchForTask\([^)]+\)'
        labor_search_matches = re.findall(labor_search_pattern, template_content)
        
        if not labor_search_matches:
            print("❌ Labor search button not found")
            return False
        
        print(f"✅ Found {len(labor_search_matches)} labor search button(s)")
        
        # Check each match
        all_correct = True
        for i, match in enumerate(labor_search_matches):
            print(f"\n🔍 Checking labor search button #{i+1}:")
            print(f"   Code: {match}")
            
            # Check if it uses task.siteid or workorder.siteid instead of user_site_id
            if 'task.siteid' in match or 'workorder.siteid' in match:
                print("   ✅ Uses work order/task site ID")
            elif 'user_site_id' in match:
                print("   ❌ Still uses user site ID")
                all_correct = False
            else:
                print("   ⚠️  Uses unknown site ID source")
                all_correct = False
        
        return all_correct
        
    except FileNotFoundError:
        print("❌ workorder_detail.html template not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing template: {e}")
        return False

def test_labor_search_service_ready():
    """
    Test that the labor search service and API endpoint are properly set up.
    """
    print("\n🔧 Testing Labor Search Service Setup")
    print("=" * 50)
    
    try:
        # Check that the labor search service exists
        from backend.services.labor_search_service import LaborSearchService
        print("✅ LaborSearchService can be imported")
        
        # Check that the API endpoint exists in app.py
        with open('app.py', 'r') as f:
            app_content = f.read()
        
        if '/api/labor/search' in app_content:
            print("✅ Labor search API endpoint exists")
        else:
            print("❌ Labor search API endpoint not found")
            return False
        
        # Check that the service is initialized
        if 'labor_search_service = LaborSearchService(token_manager)' in app_content:
            print("✅ LaborSearchService is properly initialized")
        else:
            print("❌ LaborSearchService not properly initialized")
            return False
        
        # Check that the service uses worksite field for filtering
        with open('backend/services/labor_search_service.py', 'r') as f:
            service_content = f.read()
        
        if 'worksite=' in service_content:
            print("✅ Labor search service uses 'worksite' field for filtering")
        else:
            print("❌ Labor search service does not use 'worksite' field")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_labor_search_consistency():
    """
    Test that labor search and labor loading use consistent site ID approach.
    """
    print("\n🔄 Testing Labor Search and Loading Consistency")
    print("=" * 50)
    
    try:
        # Check that both use work order site ID approach
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Find labor search call
        labor_search_match = re.search(r'openLaborSearchForTask\([^)]+\)', template_content)
        if labor_search_match:
            labor_search_call = labor_search_match.group(0)
            print(f"Labor search call: {labor_search_call}")
            
            if 'task.siteid' in labor_search_call or 'workorder.siteid' in labor_search_call:
                print("✅ Labor search uses work order/task site ID")
                search_uses_wo_site = True
            else:
                print("❌ Labor search does not use work order/task site ID")
                search_uses_wo_site = False
        else:
            print("❌ Labor search call not found")
            search_uses_wo_site = False
        
        # Check labor loading endpoint
        with open('app.py', 'r') as f:
            app_content = f.read()
        
        # Find the labor loading endpoint
        labor_endpoint_start = app_content.find('@app.route(\'/api/task/<task_wonum>/labor\'')
        if labor_endpoint_start != -1:
            labor_endpoint_end = app_content.find('@app.route', labor_endpoint_start + 1)
            if labor_endpoint_end == -1:
                labor_endpoint_end = len(app_content)
            
            labor_endpoint_code = app_content[labor_endpoint_start:labor_endpoint_end]
            
            if 'task_labor_service.get_workorder_site_id' in labor_endpoint_code:
                print("✅ Labor loading uses work order site ID")
                loading_uses_wo_site = True
            else:
                print("❌ Labor loading does not use work order site ID")
                loading_uses_wo_site = False
        else:
            print("❌ Labor loading endpoint not found")
            loading_uses_wo_site = False
        
        if search_uses_wo_site and loading_uses_wo_site:
            print("✅ Labor search and loading are consistent - both use work order site ID")
            return True
        else:
            print("❌ Labor search and loading are inconsistent")
            return False
        
    except Exception as e:
        print(f"❌ Error checking consistency: {e}")
        return False

def main():
    """Main test function"""
    print(f"🚀 Labor Search Site ID Fix Verification")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Run tests
    template_test_passed = test_labor_search_uses_workorder_site_id()
    service_test_passed = test_labor_search_service_ready()
    consistency_test_passed = test_labor_search_consistency()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS:")
    
    if template_test_passed and service_test_passed and consistency_test_passed:
        print("✅ ALL TESTS PASSED!")
        print("✅ Labor search now uses work order/task site ID instead of user site ID")
        print("✅ Labor search service is properly configured")
        print("✅ Labor search and loading are consistent")
        print("✅ Labor search will now filter by the correct site")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        if not template_test_passed:
            print("❌ Template site ID fix verification failed")
        if not service_test_passed:
            print("❌ Service setup verification failed")
        if not consistency_test_passed:
            print("❌ Consistency verification failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
