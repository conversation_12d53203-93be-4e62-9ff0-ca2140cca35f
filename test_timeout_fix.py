#!/usr/bin/env python3
"""
Test script to verify that the timeout values have been increased 
in the enhanced workorder service.
"""

import sys
import os
import re
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_timeout_values_increased():
    """
    Test that timeout values have been increased from 15 seconds to 45 seconds.
    """
    print("🧪 Testing Timeout Value Increases")
    print("=" * 50)
    
    try:
        # Read the enhanced workorder service file
        with open('backend/services/enhanced_workorder_service.py', 'r') as f:
            service_content = f.read()
        
        # Find all timeout patterns
        timeout_patterns = re.findall(r'timeout=\([^)]+\)', service_content)
        
        print(f"✅ Found {len(timeout_patterns)} timeout configurations")
        
        # Check each timeout pattern
        all_correct = True
        for i, pattern in enumerate(timeout_patterns):
            print(f"\n🔍 Checking timeout #{i+1}: {pattern}")
            
            # Extract the timeout values
            timeout_match = re.search(r'timeout=\(([^,]+),\s*([^)]+)\)', pattern)
            if timeout_match:
                connect_timeout = timeout_match.group(1).strip()
                read_timeout = timeout_match.group(2).strip()
                
                print(f"   Connect timeout: {connect_timeout}")
                print(f"   Read timeout: {read_timeout}")
                
                # Check if read timeout is reasonable (should be >= 30 seconds)
                try:
                    read_timeout_value = float(read_timeout)
                    if read_timeout_value >= 30:
                        print(f"   ✅ Read timeout is adequate ({read_timeout_value}s)")
                    else:
                        print(f"   ⚠️  Read timeout might be too short ({read_timeout_value}s)")
                        all_correct = False
                except ValueError:
                    print(f"   ⚠️  Could not parse read timeout value: {read_timeout}")
                    all_correct = False
            else:
                print(f"   ❌ Could not parse timeout values")
                all_correct = False
        
        # Check for specific methods that should have increased timeouts
        critical_methods = [
            '_execute_paginated_search',
            'get_workorder_by_wonum'
        ]
        
        print(f"\n🎯 Checking Critical Methods:")
        for method in critical_methods:
            method_start = service_content.find(f'def {method}(')
            if method_start != -1:
                # Find the end of the method (next def or end of file)
                method_end = service_content.find('\n    def ', method_start + 1)
                if method_end == -1:
                    method_end = len(service_content)
                
                method_content = service_content[method_start:method_end]
                
                # Check for timeout values in this method
                method_timeouts = re.findall(r'timeout=\([^)]+\)', method_content)
                if method_timeouts:
                    print(f"   ✅ {method}: Found {len(method_timeouts)} timeout(s)")
                    for timeout in method_timeouts:
                        print(f"      {timeout}")
                else:
                    print(f"   ⚠️  {method}: No timeouts found")
            else:
                print(f"   ❌ {method}: Method not found")
        
        return all_correct
        
    except FileNotFoundError:
        print("❌ enhanced_workorder_service.py file not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing service file: {e}")
        return False

def test_no_short_timeouts():
    """
    Test that there are no remaining short timeouts (< 20 seconds).
    """
    print("\n🚫 Testing for Short Timeouts")
    print("=" * 50)
    
    try:
        with open('backend/services/enhanced_workorder_service.py', 'r') as f:
            service_content = f.read()
        
        # Find all timeout patterns and check for short ones
        timeout_patterns = re.findall(r'timeout=\([^)]+\)', service_content)
        
        short_timeouts_found = []
        for pattern in timeout_patterns:
            timeout_match = re.search(r'timeout=\(([^,]+),\s*([^)]+)\)', pattern)
            if timeout_match:
                try:
                    read_timeout = float(timeout_match.group(2).strip())
                    if read_timeout < 20:
                        short_timeouts_found.append((pattern, read_timeout))
                except ValueError:
                    pass
        
        if not short_timeouts_found:
            print("✅ No short timeouts found (all >= 20 seconds)")
            return True
        else:
            print(f"❌ Found {len(short_timeouts_found)} short timeout(s):")
            for pattern, timeout_val in short_timeouts_found:
                print(f"   {pattern} (read timeout: {timeout_val}s)")
            return False
        
    except Exception as e:
        print(f"❌ Error checking for short timeouts: {e}")
        return False

def test_service_can_be_imported():
    """
    Test that the service can still be imported after changes.
    """
    print("\n📦 Testing Service Import")
    print("=" * 50)
    
    try:
        from backend.services.enhanced_workorder_service import EnhancedWorkOrderService
        print("✅ EnhancedWorkOrderService can be imported successfully")
        
        # Check if the class has the expected methods
        expected_methods = ['search_workorders', 'get_workorder_by_wonum', '_execute_paginated_search']
        for method in expected_methods:
            if hasattr(EnhancedWorkOrderService, method):
                print(f"✅ Method {method} exists")
            else:
                print(f"❌ Method {method} missing")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main test function"""
    print(f"🚀 Timeout Fix Verification")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Run tests
    timeout_test_passed = test_timeout_values_increased()
    short_timeout_test_passed = test_no_short_timeouts()
    import_test_passed = test_service_can_be_imported()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS:")
    
    if timeout_test_passed and short_timeout_test_passed and import_test_passed:
        print("✅ ALL TESTS PASSED!")
        print("✅ Timeout values have been increased to handle slower API responses")
        print("✅ No short timeouts remain that could cause premature failures")
        print("✅ Service can be imported and has all expected methods")
        print("✅ Work order search should now be more reliable")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        if not timeout_test_passed:
            print("❌ Timeout value verification failed")
        if not short_timeout_test_passed:
            print("❌ Short timeout check failed")
        if not import_test_passed:
            print("❌ Service import test failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
