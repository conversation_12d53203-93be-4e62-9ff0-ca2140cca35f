#!/usr/bin/env python3
"""
Test script to verify that the inventory search now uses work order site ID
instead of user site ID for filtering.
"""

import sys
import os
import re
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_inventory_search_uses_workorder_site_id():
    """
    Test that the inventory search button now uses work order/task site ID.
    """
    print("🧪 Testing Inventory Search Site ID Fix")
    print("=" * 50)
    
    try:
        # Read the workorder_detail.html template
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Find inventory search button (exclude JavaScript function definitions)
        inventory_search_pattern = r'onclick="openInventorySearchForTask\([^)]+\)"'
        inventory_search_matches = re.findall(inventory_search_pattern, template_content)
        
        if not inventory_search_matches:
            print("❌ Inventory search button not found")
            return False
        
        print(f"✅ Found {len(inventory_search_matches)} inventory search button(s)")
        
        # Check each match
        all_correct = True
        for i, match in enumerate(inventory_search_matches):
            print(f"\n🔍 Checking inventory search button #{i+1}:")
            
            # Extract just the function call part
            function_call = re.search(r'openInventorySearchForTask\([^)]+\)', match).group(0)
            print(f"   Code: {function_call}")
            
            # Check if it uses task.siteid or workorder.siteid instead of user_site_id
            if 'task.siteid' in function_call or 'workorder.siteid' in function_call:
                print("   ✅ Uses work order/task site ID")
            elif 'user_site_id' in function_call:
                print("   ❌ Still uses user site ID")
                all_correct = False
            else:
                print("   ⚠️  Uses unknown site ID source")
                all_correct = False
        
        return all_correct
        
    except FileNotFoundError:
        print("❌ workorder_detail.html template not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing template: {e}")
        return False

def test_inventory_search_consistency_with_labor():
    """
    Test that inventory search and labor search now use the same site ID approach.
    """
    print("\n🔄 Testing Inventory and Labor Search Consistency")
    print("=" * 50)
    
    try:
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Find labor search call
        labor_search_match = re.search(r'openLaborSearchForTask\([^)]+\)', template_content)
        if labor_search_match:
            labor_search_call = labor_search_match.group(0)
            print(f"Labor search call: {labor_search_call}")
            
            if 'task.siteid' in labor_search_call or 'workorder.siteid' in labor_search_call:
                print("✅ Labor search uses work order/task site ID")
                labor_uses_wo_site = True
            else:
                print("❌ Labor search does not use work order/task site ID")
                labor_uses_wo_site = False
        else:
            print("❌ Labor search call not found")
            labor_uses_wo_site = False
        
        # Find inventory search call
        inventory_search_match = re.search(r'openInventorySearchForTask\([^)]+\)', template_content)
        if inventory_search_match:
            inventory_search_call = inventory_search_match.group(0)
            print(f"Inventory search call: {inventory_search_call}")
            
            if 'task.siteid' in inventory_search_call or 'workorder.siteid' in inventory_search_call:
                print("✅ Inventory search uses work order/task site ID")
                inventory_uses_wo_site = True
            else:
                print("❌ Inventory search does not use work order/task site ID")
                inventory_uses_wo_site = False
        else:
            print("❌ Inventory search call not found")
            inventory_uses_wo_site = False
        
        if labor_uses_wo_site and inventory_uses_wo_site:
            print("✅ Labor and inventory search are consistent - both use work order site ID")
            return True
        else:
            print("❌ Labor and inventory search are inconsistent")
            return False
        
    except Exception as e:
        print(f"❌ Error checking consistency: {e}")
        return False

def test_no_user_site_id_in_inventory_search():
    """
    Test that user_site_id is no longer used in inventory search functionality.
    """
    print("\n🚫 Testing Removal of User Site ID from Inventory Search")
    print("=" * 50)
    
    try:
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Find inventory search function calls
        inventory_search_calls = re.findall(r'openInventorySearchForTask\([^)]+\)', template_content)
        
        user_site_id_found = False
        for call in inventory_search_calls:
            if 'user_site_id' in call:
                print(f"❌ Found user_site_id in inventory search: {call}")
                user_site_id_found = True
        
        if not user_site_id_found:
            print("✅ No user_site_id references found in inventory search calls")
            print("✅ Inventory search now uses work order site ID")
            return True
        else:
            print("❌ Inventory search still uses user_site_id")
            return False
        
    except Exception as e:
        print(f"❌ Error checking user_site_id references: {e}")
        return False

def main():
    """Main test function"""
    print(f"🚀 Inventory Search Site ID Fix Verification")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Run tests
    search_test_passed = test_inventory_search_uses_workorder_site_id()
    consistency_test_passed = test_inventory_search_consistency_with_labor()
    cleanup_test_passed = test_no_user_site_id_in_inventory_search()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS:")
    
    if search_test_passed and consistency_test_passed and cleanup_test_passed:
        print("✅ ALL TESTS PASSED!")
        print("✅ Inventory search now uses work order site ID instead of user site ID")
        print("✅ Inventory search is consistent with labor search")
        print("✅ No user site ID references remain in inventory search")
        print("✅ Users can now search inventory based on work order location")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        if not search_test_passed:
            print("❌ Inventory search fix verification failed")
        if not consistency_test_passed:
            print("❌ Consistency verification failed")
        if not cleanup_test_passed:
            print("❌ User site ID cleanup verification failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
