#!/usr/bin/env python3
"""
Test script to verify that both labor search and inventory search now use 
work order site ID instead of user site ID for filtering.
"""

import sys
import os
import re
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_search_buttons_use_workorder_site_id():
    """
    Test that both labor and inventory search buttons now use work order/task site ID.
    """
    print("🧪 Testing Search Buttons Site ID Fix")
    print("=" * 50)
    
    try:
        # Read the workorder_detail.html template
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Find labor search button
        labor_search_pattern = r'openLaborSearchForTask\([^)]+\)'
        labor_search_matches = re.findall(labor_search_pattern, template_content)
        
        # Find inventory search button (exclude JavaScript function definitions)
        inventory_search_pattern = r'onclick="openInventorySearchForTask\([^)]+\)"'
        inventory_search_matches = re.findall(inventory_search_pattern, template_content)
        # Extract just the function call part
        inventory_search_matches = [re.search(r'openInventorySearchForTask\([^)]+\)', match).group(0) for match in inventory_search_matches]
        
        print(f"✅ Found {len(labor_search_matches)} labor search button(s)")
        print(f"✅ Found {len(inventory_search_matches)} inventory search button(s)")
        
        all_correct = True
        
        # Check labor search buttons
        for i, match in enumerate(labor_search_matches):
            print(f"\n🔍 Checking labor search button #{i+1}:")
            print(f"   Code: {match}")
            
            if 'task.siteid' in match or 'workorder.siteid' in match:
                print("   ✅ Uses work order/task site ID")
            elif 'user_site_id' in match:
                print("   ❌ Still uses user site ID")
                all_correct = False
            else:
                print("   ⚠️  Uses unknown site ID source")
                all_correct = False
        
        # Check inventory search buttons
        for i, match in enumerate(inventory_search_matches):
            print(f"\n🔍 Checking inventory search button #{i+1}:")
            print(f"   Code: {match}")
            
            if 'task.siteid' in match or 'workorder.siteid' in match:
                print("   ✅ Uses work order/task site ID")
            elif 'user_site_id' in match:
                print("   ❌ Still uses user site ID")
                all_correct = False
            else:
                print("   ⚠️  Uses unknown site ID source")
                all_correct = False
        
        return all_correct
        
    except FileNotFoundError:
        print("❌ workorder_detail.html template not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing template: {e}")
        return False

def test_consistency_across_all_functionality():
    """
    Test that labor loading, materials loading, labor search, and inventory search 
    all use consistent site ID approach.
    """
    print("\n🔄 Testing Consistency Across All Functionality")
    print("=" * 50)
    
    try:
        # Check app.py for API endpoints
        with open('app.py', 'r') as f:
            app_content = f.read()
        
        # Check labor loading endpoint
        labor_endpoint_start = app_content.find('@app.route(\'/api/task/<task_wonum>/labor\'')
        if labor_endpoint_start != -1:
            labor_endpoint_end = app_content.find('@app.route', labor_endpoint_start + 1)
            if labor_endpoint_end == -1:
                labor_endpoint_end = len(app_content)
            
            labor_endpoint_code = app_content[labor_endpoint_start:labor_endpoint_end]
            
            if 'task_labor_service.get_workorder_site_id' in labor_endpoint_code:
                print("✅ Labor loading uses work order site ID")
                labor_loading_correct = True
            else:
                print("❌ Labor loading does not use work order site ID")
                labor_loading_correct = False
        else:
            print("❌ Labor loading endpoint not found")
            labor_loading_correct = False
        
        # Check materials loading endpoint
        materials_endpoint_start = app_content.find('@app.route(\'/api/task/<task_wonum>/planned-materials\'')
        if materials_endpoint_start != -1:
            materials_endpoint_end = app_content.find('@app.route', materials_endpoint_start + 1)
            if materials_endpoint_end == -1:
                materials_endpoint_end = len(app_content)
            
            materials_endpoint_code = app_content[materials_endpoint_start:materials_endpoint_end]
            
            if 'task_materials_service.get_workorder_site_id' in materials_endpoint_code:
                print("✅ Materials loading uses work order site ID")
                materials_loading_correct = True
            else:
                print("❌ Materials loading does not use work order site ID")
                materials_loading_correct = False
        else:
            print("❌ Materials loading endpoint not found")
            materials_loading_correct = False
        
        # Check frontend template
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Check labor search
        labor_search_match = re.search(r'openLaborSearchForTask\([^)]+\)', template_content)
        if labor_search_match and ('task.siteid' in labor_search_match.group(0) or 'workorder.siteid' in labor_search_match.group(0)):
            print("✅ Labor search uses work order/task site ID")
            labor_search_correct = True
        else:
            print("❌ Labor search does not use work order/task site ID")
            labor_search_correct = False
        
        # Check inventory search
        inventory_search_match = re.search(r'openInventorySearchForTask\([^)]+\)', template_content)
        if inventory_search_match and ('task.siteid' in inventory_search_match.group(0) or 'workorder.siteid' in inventory_search_match.group(0)):
            print("✅ Inventory search uses work order/task site ID")
            inventory_search_correct = True
        else:
            print("❌ Inventory search does not use work order/task site ID")
            inventory_search_correct = False
        
        # Summary
        all_consistent = labor_loading_correct and materials_loading_correct and labor_search_correct and inventory_search_correct
        
        if all_consistent:
            print("\n🎯 CONSISTENCY SUMMARY:")
            print("✅ Labor loading: Uses work order site ID")
            print("✅ Materials loading: Uses work order site ID")
            print("✅ Labor search: Uses work order site ID")
            print("✅ Inventory search: Uses work order site ID")
            print("✅ ALL FUNCTIONALITY IS CONSISTENT!")
        else:
            print("\n⚠️  CONSISTENCY ISSUES FOUND:")
            if not labor_loading_correct:
                print("❌ Labor loading inconsistent")
            if not materials_loading_correct:
                print("❌ Materials loading inconsistent")
            if not labor_search_correct:
                print("❌ Labor search inconsistent")
            if not inventory_search_correct:
                print("❌ Inventory search inconsistent")
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ Error checking consistency: {e}")
        return False

def test_no_user_site_id_references():
    """
    Test that user_site_id is no longer used in search functionality.
    """
    print("\n🚫 Testing Removal of User Site ID References")
    print("=" * 50)
    
    try:
        with open('frontend/templates/workorder_detail.html', 'r') as f:
            template_content = f.read()
        
        # Find all search function calls
        search_calls = re.findall(r'open(?:Labor|Inventory)SearchForTask\([^)]+\)', template_content)
        
        user_site_id_found = False
        for call in search_calls:
            if 'user_site_id' in call:
                print(f"❌ Found user_site_id in: {call}")
                user_site_id_found = True
        
        if not user_site_id_found:
            print("✅ No user_site_id references found in search calls")
            print("✅ All search functionality now uses work order site ID")
            return True
        else:
            print("❌ Some search calls still use user_site_id")
            return False
        
    except Exception as e:
        print(f"❌ Error checking user_site_id references: {e}")
        return False

def main():
    """Main test function"""
    print(f"🚀 Complete Search Site ID Fix Verification")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Run tests
    buttons_test_passed = test_search_buttons_use_workorder_site_id()
    consistency_test_passed = test_consistency_across_all_functionality()
    cleanup_test_passed = test_no_user_site_id_references()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS:")
    
    if buttons_test_passed and consistency_test_passed and cleanup_test_passed:
        print("✅ ALL TESTS PASSED!")
        print("✅ Both labor search and inventory search now use work order site ID")
        print("✅ All functionality (loading + search) is consistent")
        print("✅ No user site ID references remain in search functionality")
        print("✅ Users can now search across different sites based on work order location")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        if not buttons_test_passed:
            print("❌ Search button fix verification failed")
        if not consistency_test_passed:
            print("❌ Consistency verification failed")
        if not cleanup_test_passed:
            print("❌ User site ID cleanup verification failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
