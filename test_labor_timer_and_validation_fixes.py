#!/usr/bin/env python3
"""
Test script to verify that the labor timer and validation fixes work correctly.

This script tests:
1. Timer calculation logic uses elapsed seconds instead of dropdown calculation
2. Backend validation for regular hours > 0 has been removed
3. Frontend validation min="0.25" has been removed
"""

import sys
import os
import re
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_timer_calculation_fix():
    """
    Test that the timer now uses elapsed seconds for hour calculation.
    """
    print("🧪 Testing Timer Calculation Fix")
    print("=" * 50)
    
    try:
        # Read the labor_search.js file
        with open('frontend/static/js/labor_search.js', 'r') as f:
            js_content = f.read()
        
        # Find the stopTimer method
        stop_timer_start = js_content.find('stopTimer() {')
        if stop_timer_start == -1:
            print("❌ stopTimer method not found")
            return False
        
        # Find the end of the method (next method or end of file)
        stop_timer_end = js_content.find('\n    }', stop_timer_start + 1)
        if stop_timer_end == -1:
            stop_timer_end = len(js_content)
        
        stop_timer_code = js_content[stop_timer_start:stop_timer_end]
        
        # Check for the fix: using elapsed seconds for calculation
        if 'this.elapsedSeconds / 3600' in stop_timer_code:
            print("✅ Timer now uses elapsed seconds for hour calculation")
        else:
            print("❌ Timer still uses dropdown calculation")
            return False
        
        # Check for the comment explaining the fix
        if 'Use actual elapsed time from timer instead of dropdown calculation' in stop_timer_code:
            print("✅ Code includes explanatory comment about the fix")
        else:
            print("⚠️  No explanatory comment found (not critical)")
        
        # Check that fallback to dropdown calculation exists
        if 'this.calculateHours()' in stop_timer_code:
            print("✅ Fallback to dropdown calculation exists")
        else:
            print("❌ No fallback to dropdown calculation")
            return False
        
        return True
        
    except FileNotFoundError:
        print("❌ labor_search.js file not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing JavaScript file: {e}")
        return False

def test_backend_validation_removed():
    """
    Test that backend validation for regular hours > 0 has been removed.
    """
    print("\n🧪 Testing Backend Validation Removal")
    print("=" * 50)
    
    try:
        # Read the labor_request_service.py file
        with open('backend/services/labor_request_service.py', 'r') as f:
            service_content = f.read()
        
        # Check that the validation for regularhrs > 0 has been removed
        if 'regularhrs <= 0' in service_content:
            print("❌ Backend still validates regularhrs > 0")
            return False
        else:
            print("✅ Backend validation for regularhrs > 0 has been removed")
        
        # Check that the validation comment exists
        if 'Removed validation for regularhrs > 0 as requested' in service_content:
            print("✅ Code includes explanatory comment about validation removal")
        else:
            print("⚠️  No explanatory comment found (not critical)")
        
        # Check that basic type validation still exists
        if 'float(regularhrs)' in service_content:
            print("✅ Basic type validation for regularhrs still exists")
        else:
            print("❌ Basic type validation for regularhrs missing")
            return False
        
        return True
        
    except FileNotFoundError:
        print("❌ labor_request_service.py file not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing service file: {e}")
        return False

def test_frontend_validation_removed():
    """
    Test that frontend validation min="0.25" has been removed.
    """
    print("\n🧪 Testing Frontend Validation Removal")
    print("=" * 50)
    
    try:
        # Read the labor_search_modal.html file
        with open('frontend/templates/components/labor_search_modal.html', 'r') as f:
            template_content = f.read()
        
        # Find the laborHours input field
        labor_hours_pattern = r'<input[^>]*id="laborHours"[^>]*>'
        labor_hours_match = re.search(labor_hours_pattern, template_content)
        
        if not labor_hours_match:
            print("❌ laborHours input field not found")
            return False
        
        labor_hours_input = labor_hours_match.group(0)
        print(f"Found laborHours input: {labor_hours_input}")
        
        # Check that min="0.25" has been removed
        if 'min="0.25"' in labor_hours_input:
            print("❌ Frontend still has min='0.25' validation")
            return False
        else:
            print("✅ Frontend validation min='0.25' has been removed")
        
        # Check that step has been changed to 0.01 for more precision
        if 'step="0.01"' in labor_hours_input:
            print("✅ Step changed to 0.01 for better precision")
        else:
            print("⚠️  Step not changed to 0.01 (not critical)")
        
        # Check that required attribute still exists
        if 'required' in labor_hours_input:
            print("✅ Required attribute still exists")
        else:
            print("❌ Required attribute missing")
            return False
        
        return True
        
    except FileNotFoundError:
        print("❌ labor_search_modal.html file not found")
        return False
    except Exception as e:
        print(f"❌ Error analyzing template file: {e}")
        return False

def test_files_can_be_loaded():
    """
    Test that all modified files can be loaded without syntax errors.
    """
    print("\n🧪 Testing File Syntax")
    print("=" * 50)
    
    try:
        # Test JavaScript syntax (basic check)
        with open('frontend/static/js/labor_search.js', 'r') as f:
            js_content = f.read()
        
        # Basic syntax checks for JavaScript
        if js_content.count('{') != js_content.count('}'):
            print("❌ JavaScript file has mismatched braces")
            return False
        else:
            print("✅ JavaScript file has balanced braces")
        
        # Test Python syntax
        try:
            from backend.services.labor_request_service import LaborRequestService
            print("✅ LaborRequestService can be imported")
        except ImportError as e:
            print(f"❌ Cannot import LaborRequestService: {e}")
            return False
        except SyntaxError as e:
            print(f"❌ Syntax error in LaborRequestService: {e}")
            return False
        
        # Test HTML template (basic check)
        with open('frontend/templates/components/labor_search_modal.html', 'r') as f:
            html_content = f.read()
        
        # Basic HTML validation
        if '<input' in html_content and 'id="laborHours"' in html_content:
            print("✅ HTML template contains laborHours input field")
        else:
            print("❌ HTML template missing laborHours input field")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing file syntax: {e}")
        return False

def main():
    """Main test function"""
    print(f"🚀 Labor Timer and Validation Fixes Verification")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Run tests
    timer_test_passed = test_timer_calculation_fix()
    backend_test_passed = test_backend_validation_removed()
    frontend_test_passed = test_frontend_validation_removed()
    syntax_test_passed = test_files_can_be_loaded()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS:")
    
    if timer_test_passed and backend_test_passed and frontend_test_passed and syntax_test_passed:
        print("✅ ALL TESTS PASSED!")
        print("✅ Timer now uses actual elapsed seconds for accurate time tracking")
        print("✅ Backend validation for regularhrs > 0 has been removed")
        print("✅ Frontend validation min='0.25' has been removed")
        print("✅ All files have valid syntax and can be loaded")
        print("\n🎯 FIXES SUMMARY:")
        print("   • Timer display now matches recorded time exactly")
        print("   • Any positive value for regular hours is accepted")
        print("   • More precise time entry with 0.01 step increments")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        if not timer_test_passed:
            print("❌ Timer calculation fix verification failed")
        if not backend_test_passed:
            print("❌ Backend validation removal verification failed")
        if not frontend_test_passed:
            print("❌ Frontend validation removal verification failed")
        if not syntax_test_passed:
            print("❌ File syntax verification failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
