#!/usr/bin/env python3
"""
Labor Deletion Service for Work Orders
Handles deletion and adjustment of labor entries using MXAPIWODETAIL API
"""

import logging
import time
import json
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)

class LaborDeletionService:
    """
    Service for deleting or adjusting labor entries in work orders.
    
    This service handles:
    - Complete deletion of labor entries
    - Partial hour adjustments using negative values
    - Session-based authentication using token manager
    - Proper payload construction following Maximo API requirements
    """
    
    def __init__(self, token_manager, task_labor_service=None):
        """Initialize the labor deletion service."""
        self.token_manager = token_manager
        self.task_labor_service = task_labor_service
        self.logger = logger
        
        # Performance tracking
        self._request_count = 0
        self._success_count = 0
        self._total_time = 0.0
        
    def add_negative_labor_hours(self, task_wonum: str, parent_wonum: str, laborcode: str,
                                negative_hours: float, taskid: int, siteid: str,
                                craft: str = None) -> Dict[str, Any]:
        """
        Add negative labor hours to offset existing entries.
        This is simpler than deleting - just add a new entry with negative hours.

        Args:
            task_wonum: Task work order number (for logging/reference only)
            parent_wonum: Parent work order number (used in payload)
            laborcode: Labor code to add negative hours for
            negative_hours: Negative hours to add (should be negative, e.g., -0.1)
            taskid: Task ID (numeric task identifier)
            siteid: Site ID
            craft: Optional craft specification

        Returns:
            Dict containing success status and response data
        """
        start_time = time.time()

        try:
            # Validate session
            if not self.is_session_valid():
                self.logger.error("Cannot add negative labor: Not logged in")
                return {'success': False, 'error': 'Not logged in'}

            # Validate required parameters
            if not all([task_wonum, parent_wonum, laborcode, taskid, siteid]):
                missing = [param for param, value in [
                    ('task_wonum', task_wonum), ('parent_wonum', parent_wonum), ('laborcode', laborcode),
                    ('taskid', taskid), ('siteid', siteid)
                ] if not value]
                self.logger.error(f"Missing required parameters: {missing}")
                return {'success': False, 'error': f'Missing required parameters: {missing}'}

            # Ensure hours are negative
            if negative_hours >= 0:
                negative_hours = -abs(negative_hours)

            self.logger.info(f"🔧 NEGATIVE LABOR: Adding {negative_hours} hours for {laborcode} to task {task_wonum}")

            # Add negative hours using the same pattern as regular labor addition
            result = self._add_negative_labor_entry(task_wonum, parent_wonum, laborcode, negative_hours, taskid, siteid, craft)

            # Update performance stats
            request_time = time.time() - start_time
            self._update_performance_stats(request_time, result.get('success', False))

            # Clear cache after successful operation
            if result.get('success') and self.task_labor_service:
                self.task_labor_service.clear_cache()

            return result

        except Exception as e:
            request_time = time.time() - start_time
            self._update_performance_stats(request_time, False)
            self.logger.error(f"Exception in add_negative_labor_hours: {e}")
            return {'success': False, 'error': f'Unexpected error: {str(e)}'}
    
    def _add_negative_labor_entry(self, task_wonum: str, parent_wonum: str, laborcode: str,
                                 negative_hours: float, taskid: int, siteid: str, craft: str = None) -> Dict[str, Any]:
        """Add a negative labor entry using the same pattern as regular labor addition."""
        try:
            # Build the payload for adding negative hours (EXACT same structure as regular labor addition)
            labor_payload = {
                "laborcode": laborcode,
                "regularhrs": negative_hours,  # This will be negative, e.g., -0.1
                "taskid": taskid,
                "genapprservreceipt": 1,  # Always approved like regular labor
                "transtype": "WORK"
            }

            # Create AddChange payload (EXACT same structure as regular labor addition)
            # CRITICAL: Use parent_wonum, not task_wonum in the payload
            addchange_payload = [{
                "_action": "AddChange",
                "wonum": parent_wonum,  # Use parent work order number, not task wonum
                "siteid": siteid,
                "labtrans": [labor_payload]
            }]

            self.logger.info(f"🔧 NEGATIVE LABOR: Adding negative hours payload: {json.dumps(addchange_payload, indent=2)}")

            # Make the API request using the same method as regular labor addition
            result = self._make_addchange_request(addchange_payload)

            if result.get('success'):
                result['message'] = f'Added {negative_hours} hours for {laborcode} (negative adjustment)'
                result['negative_hours_added'] = negative_hours

            return result

        except Exception as e:
            self.logger.error(f"Error adding negative labor entry: {e}")
            return {'success': False, 'error': f'Failed to add negative labor entry: {str(e)}'}
    

    
    def _make_addchange_request(self, payload: Dict) -> Dict[str, Any]:
        """Make AddChange request to add negative hours."""
        try:
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"
            
            params = {
                'lean': '1',
                'ignorecollectionref': '1',
                'ignorekeyref': '1',
                'ignorers': '1',
                'mxlaction': 'addchange'
            }
            
            headers = {
                'x-method-override': 'BULK',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            self.logger.info(f"🔧 LABOR DELETION: Making AddChange request for negative hours")
            self.logger.debug(f"🔧 LABOR DELETION: Payload: {json.dumps(payload, indent=2)}")
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                params=params,
                headers=headers,
                timeout=(3.05, 30)
            )
            
            return self._process_addchange_response(response)
            
        except Exception as e:
            self.logger.error(f"Error making AddChange request: {e}")
            return {'success': False, 'error': f'Request failed: {str(e)}'}
    
    def _process_addchange_response(self, response) -> Dict[str, Any]:
        """Process the AddChange response."""
        try:
            if response.status_code != 200:
                return {'success': False, 'error': f'HTTP {response.status_code}: {response.text}'}
            
            result_data = response.json()
            
            if isinstance(result_data, list) and len(result_data) > 0:
                response_data = result_data[0]
                if '_responsedata' in response_data and 'Error' in response_data['_responsedata']:
                    error = response_data['_responsedata']['Error']
                    error_message = error.get('message', 'Unknown error')
                    error_code = error.get('reasonCode', 'Unknown code')
                    return {
                        'success': False,
                        'error': f"Maximo Error [{error_code}]: {error_message}",
                        'error_code': error_code
                    }
                elif '_responsemeta' in response_data and response_data['_responsemeta'].get('status') == '204':
                    return {
                        'success': True,
                        'message': 'Labor hours adjusted successfully',
                        'data': result_data
                    }
            
            return {'success': False, 'error': 'Unexpected response format'}
            
        except Exception as e:
            self.logger.error(f"Error processing response: {e}")
            return {'success': False, 'error': f'Response processing failed: {str(e)}'}
    
    def is_session_valid(self) -> bool:
        """Check if the session is valid."""
        return (hasattr(self.token_manager, 'username') and 
                self.token_manager.username and 
                hasattr(self.token_manager, 'session') and 
                self.token_manager.session)
    
    def _update_performance_stats(self, request_time: float, success: bool):
        """Update performance statistics."""
        self._request_count += 1
        self._total_time += request_time
        if success:
            self._success_count += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        avg_time = self._total_time / self._request_count if self._request_count > 0 else 0
        success_rate = (self._success_count / self._request_count * 100) if self._request_count > 0 else 0
        
        return {
            'total_requests': self._request_count,
            'successful_requests': self._success_count,
            'success_rate_percent': round(success_rate, 2),
            'average_response_time_seconds': round(avg_time, 3),
            'total_time_seconds': round(self._total_time, 3)
        }
