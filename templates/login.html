{% extends 'base.html' %}

{% block title %}Login - <PERSON><PERSON>{% endblock %}

{% block content %}
<div class="row justify-content-center align-items-center" style="min-height: 80vh;">
    <div class="col-md-6 col-lg-5 col-xl-4">
        <div class="text-center mb-4">
            <i class="fas fa-key fa-3x text-primary mb-3"></i>
            <h2 class="fw-bold"><PERSON><PERSON> OAuth</h2>
        </div>

        <div class="card shadow border-0">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <h5 class="card-title fw-bold">Login to Test (UAT) Maximo</h5>
                    <div class="badge bg-warning text-dark mb-3">UAT Environment</div>
                    <p class="card-text text-muted">Enter your credentials to access the system</p>
                </div>

                <form method="POST" action="{{ url_for('login') }}">
                    <div class="mb-4">
                        <label for="maximo_url" class="form-label text-muted small">
                            <i class="fas fa-server me-1"></i>Maximo Server URL
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-server text-primary"></i>
                            </span>
                            <input type="url" class="form-control border-start-0" id="maximo_url" name="maximo_url"
                                   placeholder="https://your-maximo-server.com/maximo"
                                   value="{{ current_maximo_url or '' }}" required>
                        </div>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle me-1"></i>You can change this URL to connect to different Maximo environments
                        </small>
                    </div>
                    <div class="mb-4">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-user text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0" id="username" name="username" placeholder="Username" required>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-lock text-primary"></i>
                            </span>
                            <input type="password" class="form-control border-start-0" id="password" name="password" placeholder="Password" required>
                        </div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                        <label class="form-check-label" for="rememberMe">Remember me</label>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Lightning-Fast Login
                        </button>
                    </div>
                </form>

                <div class="mt-4 text-center">
                    <div class="form-check form-switch d-inline-block me-3">
                        <input class="form-check-input" type="checkbox" id="themeSwitch">
                        <label class="form-check-label" for="themeSwitch">Dark Mode</label>
                    </div>
                    <div class="form-check form-switch d-inline-block">
                        <input class="form-check-input" type="checkbox" id="urlToggle" checked>
                        <label class="form-check-label" for="urlToggle">Show URL Field</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-3">
            <small class="text-muted">Secure connection to Maximo Environment</small>
            <br>
            <small class="text-muted" id="currentUrlDisplay">
                <i class="fas fa-link me-1"></i>Current URL: <span class="text-primary">{{ current_maximo_url or 'Not set' }}</span>
            </small>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const urlToggle = document.getElementById('urlToggle');
    const urlField = document.querySelector('.mb-4:first-child'); // The URL field container
    const urlInput = document.getElementById('maximo_url');

    // Function to toggle URL field visibility
    function toggleUrlField() {
        if (urlToggle.checked) {
            urlField.style.display = 'block';
            urlInput.required = true;
        } else {
            urlField.style.display = 'none';
            urlInput.required = false;
        }
    }

    // Initial state
    toggleUrlField();

    // Toggle event listener
    urlToggle.addEventListener('change', toggleUrlField);

    // URL validation
    urlInput.addEventListener('blur', function() {
        const url = this.value.trim();
        if (url && !url.match(/^https?:\/\/.+/)) {
            this.setCustomValidity('Please enter a valid URL starting with http:// or https://');
        } else {
            this.setCustomValidity('');
        }
    });

    // Auto-format URL and update display
    urlInput.addEventListener('input', function() {
        let url = this.value.trim();
        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
            // Auto-add https:// if no protocol specified
            if (url.includes('.')) {
                this.value = 'https://' + url;
            }
        }

        // Update current URL display
        const currentUrlSpan = document.querySelector('#currentUrlDisplay span');
        if (currentUrlSpan && this.value.trim()) {
            currentUrlSpan.textContent = this.value.trim();
        }
    });

    // Form submission validation
    document.querySelector('form').addEventListener('submit', function(e) {
        if (urlToggle.checked) {
            const url = urlInput.value.trim();
            if (!url || !url.match(/^https?:\/\/.+/)) {
                e.preventDefault();
                urlInput.focus();
                alert('Please enter a valid Maximo URL');
                return false;
            }
        } else {
            // When URL field is hidden, ensure we still have a value
            if (!urlInput.value.trim()) {
                urlInput.value = '{{ current_maximo_url or "" }}';
            }
        }
    });
});
</script>
{% endblock %}
