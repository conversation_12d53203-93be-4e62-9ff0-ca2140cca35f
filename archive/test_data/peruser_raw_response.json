{"oslc:responseInfo": {"oslc:nextPage": {"rdf:resource": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser?pageno=2&oslc.where=status%3D%22ACTIVE%22&oslc.pageSize=50&lean=0&oslc.select=*"}, "totalPages": 102, "oslc:totalCount": 5095, "pagenum": 1, "rdf:about": "http://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser?lean=0&oslc.pageSize=50&oslc.select=%2A&oslc.where=status%3D%22ACTIVE%22"}, "rdfs:member": [{"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "MOOR382170", "spi:plusggastestauth": false, "_rowstamp": "2418906535", "spi:plusgtechauthority": false, "spi:employeetype_description": "NON-Employee", "spi:ud_subcontractor_description": "CONSULTANT", "spi:ud_subcontractor": "CONSULTANT", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "_rowstamp": "2419063299", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1449350710", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168/groupuser/0-2369576", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168/groupuser/0-2369576/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369576, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0JDTEFTRU4-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1449350711", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168/groupuser/1-2369575", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168/groupuser/1-2369575/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369575, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0JDTEFTRU4-"}, {"spi:groupname": "ADMINTECHSPT1", "_rowstamp": "1481134487", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168/groupuser/2-2369603", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168/groupuser/2-2369603/maxgroup/0-179", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "Maximo Support technical admins with user password rights", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0FETUlOVEVDSFNQVDE-", "spi:pluspauthperslst": false, "_rowstamp": "1481132857", "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:authallgls": true, "spi:sctemplateid": 23, "spi:independent": true, "spi:authlaborall": true, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": true, "spi:maxgroupid": 179, "spi:pluspauthnoncust": false, "spi:authallrepfacs": true}], "spi:groupuserid": 2369603, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0FETUlOVEVDSFNQVDEvQkNMQVNFTg--"}, {"spi:groupname": "IMSADMINS", "_rowstamp": "2024115608", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168/groupuser/3-2371557", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkNMQVNFTg--/user/0-1172168/groupuser/3-2371557/maxgroup/0-283", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "IMS Maximo Administrators", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0lNU0FETUlOUw--", "spi:pluspauthperslst": false, "_rowstamp": "2397601432", "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:authallgls": true, "spi:sctemplateid": 2, "spi:independent": true, "spi:authlaborall": true, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": true, "spi:maxgroupid": 283, "spi:pluspauthnoncust": false, "spi:authallrepfacs": true}], "spi:groupuserid": 2371557, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0lNU0FETUlOUy9CQ0xBU0VO"}], "spi:masissuer": "local", "spi:screenreader": false, "spi:ud_type": "MOD", "spi:querywithsite": true, "spi:sidenav": 0, "spi:isconsultant": false, "spi:ud_type_description": "Modification", "spi:inactivesites": false, "spi:userid": "BCLASEN", "spi:password": "", "spi:loginid": "b<PERSON><PERSON>n", "spi:maxuserid": 1172168, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvQkNMQVNFTg--", "spi:sysuser": false, "spi:defsite": "KDFAC", "spi:ud_ticket": "RITM0038031", "spi:status_description": "Active", "spi:type_description": "Maximo Admins (Use for Maximo developers only)", "spi:type": "TYPE 10"}], "spi:plusgsitecheckerauth": false, "spi:title": "Consultant", "spi:status_description": "Active", "spi:deviceclass": 2, "spi:employeetype": "NON-EMPL", "spi:status": "ACTIVE", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "BCLASEN", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-14T01:48:08+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989288, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2413873389", "spi:plusgtechauthority": false, "spi:employeetype_description": "NON-Employee", "spi:ud_subcontractor_description": "CONSULTANT", "spi:ud_subcontractor": "CONSULTANT", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UENYSU5UQURN", "spi:displayname": "pcxintadm", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UENYSU5UQURN/user/0-1172826", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "_rowstamp": "2413873387", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "2398555335", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UENYSU5UQURN/user/0-1172826/groupuser/0-2374267", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UENYSU5UQURN/user/0-1172826/groupuser/0-2374267/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2374267, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1BDWElOVEFETQ--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "2398555337", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UENYSU5UQURN/user/0-1172826/groupuser/1-2374266", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UENYSU5UQURN/user/0-1172826/groupuser/1-2374266/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2374266, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1BDWElOVEFETQ--"}, {"spi:groupname": "pcxadmin", "_rowstamp": "2413873390", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UENYSU5UQURN/user/0-1172826/groupuser/2-2374280", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UENYSU5UQURN/user/0-1172826/groupuser/2-2374280/maxgroup/0-300", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "ProcureX Integration Admin", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL3BjeGFkbWlu", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "2413875627", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": true, "spi:maxgroupid": 300, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2374280, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL3BjeGFkbWluL1BDWElOVEFETQ--"}], "spi:masissuer": "local", "spi:screenreader": false, "spi:ud_type": "MOD", "spi:querywithsite": true, "spi:sidenav": 0, "spi:isconsultant": false, "spi:ud_type_description": "Modification", "spi:inactivesites": false, "spi:userid": "PCXINTADM", "spi:password": "", "spi:loginid": "pcxintadm", "spi:maxuserid": 1172826, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUENYSU5UQURN", "spi:sysuser": false, "spi:defsite": "LCVKWT", "spi:ud_ticket": "123", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "EST", "spi:deviceclass": 2, "spi:employeetype": "NON-EMPL", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "EST (UTC-5)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "PCXINTADM", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2025-04-16T17:16:46+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 990942, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "1001", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "1851085564", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--", "spi:displayname": "Slade, <PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "<EMAIL>", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172170, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvTUFFLlNMQURFQEdPVjJYLkNPTQ--", "spi:sysuser": false, "spi:defsite": "LCVKWT", "_rowstamp": "1483177109", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1482363263", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/0-2369608", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/0-2369608/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369608, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL01BRS5TTEFERUBHT1YyWC5DT00-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1482363266", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/1-2369607", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/1-2369607/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369607, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL01BRS5TTEFERUBHT1YyWC5DT00-"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "1482671325", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/2-2369609", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/2-2369609/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369609, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL01BRS5TTEFERUBHT1YyWC5DT00-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1482672679", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/3-2369610", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/3-2369610/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369610, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvTUFFLlNMQURFQEdPVjJYLkNPTQ--"}, {"spi:groupname": "WO_READ_RUNREPORTS", "_rowstamp": "1482675427", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/4-2369611", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/4-2369611/maxgroup/0-128", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "1146222668", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 128, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Work Tracking read only and run reports", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dPX1JFQURfUlVOUkVQT1JUUw--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 2369611, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dPX1JFQURfUlVOUkVQT1JUUy9NQUUuU0xBREVAR09WMlguQ09N"}, {"spi:groupname": "DTR-APPROVER", "_rowstamp": "1482677267", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/5-2369612", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/5-2369612/maxgroup/0-137", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Approver", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1BUFBST1ZFUg--", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "106119764", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 137, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369612, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1BUFBST1ZFUi9NQUUuU0xBREVAR09WMlguQ09N"}, {"spi:groupname": "WHSE-Specialist", "_rowstamp": "1482678795", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/6-2369613", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFFLlNMQURFQEdPVjJYLkNPTQ--/user/0-1172170/groupuser/6-2369613/maxgroup/0-57", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "193353218", "spi:authallgls": false, "spi:sctemplateid": 39, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 57, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Warehouse Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0UtU3BlY2lhbGlzdA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369613, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0UtU3BlY2lhbGlzdC9NQUUuU0xBREVAR09WMlguQ09N"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:memo": "Admin activation by User michael.allen", "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "GCSS-Army Operator", "spi:status_description": "Active", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:department": "1600 - Materiel Management (MM", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "<EMAIL>", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-19T05:06:01+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989294, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "7757", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2049585966", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2", "spi:displayname": "<PERSON><PERSON><PERSON>, <PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "GEDD57426", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172171, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR0VERDU3NDI2", "spi:sysuser": false, "spi:defsite": "LCVKWT", "_rowstamp": "2386863275", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1482946739", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/0-2369616", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/0-2369616/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369616, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0dFREQ1NzQyNg--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1482946740", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/1-2369615", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/1-2369615/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369615, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0dFREQ1NzQyNg--"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "1483080522", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/2-2369617", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/2-2369617/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369617, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL0dFREQ1NzQyNg--"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "1483095885", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/3-2369618", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/3-2369618/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 2369618, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9HRURENTc0MjY-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1483095886", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/4-2369619", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0VERDU3NDI2/user/0-1172171/groupuser/4-2369619/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369619, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvR0VERDU3NDI2"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:memo": "Admin activation by User michael.allen", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:department": "5300 - Engineering Services", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "GEDD57426", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-19T11:49:39+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989296, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2383627381", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Ward", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-", "spi:displayname": "Ward, Bryan", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "_rowstamp": "2383627379", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1487113712", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/0-2369635", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/0-2369635/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369635, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1dBUkQzODM1NzM2"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1487113718", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/1-2369632", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/1-2369632/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369632, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1dBUkQzODM1NzM2"}, {"spi:groupname": "NSGBAUSERS", "_rowstamp": "1487270445", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/2-2369641", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/2-2369641/maxgroup/0-117", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018775", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 117, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "NSGBA Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL05TR0JBVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369641, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL05TR0JBVVNFUlMvV0FSRDM4MzU3MzY-"}, {"spi:groupname": "PROC-BUYER", "_rowstamp": "1487322712", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/3-2369652", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/3-2369652/maxgroup/0-86", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "182061985", "spi:authallgls": false, "spi:sctemplateid": 27, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 86, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Procurement Buyers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BST0MtQlVZRVI-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369652, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BST0MtQlVZRVIvV0FSRDM4MzU3MzY-"}, {"spi:groupname": "KWAJUSERS", "_rowstamp": "1487270447", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/4-2369639", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/4-2369639/maxgroup/0-165", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "287494999", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 165, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "KWAJ Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0tXQUpVU0VSUw--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369639, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0tXQUpVU0VSUy9XQVJEMzgzNTczNg--"}, {"spi:groupname": "LGCPHUSERS", "_rowstamp": "1487319796", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/5-2369651", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/5-2369651/maxgroup/0-173", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "369110109", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 173, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Philippines Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xHQ1BIVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369651, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xHQ1BIVVNFUlMvV0FSRDM4MzU3MzY-"}, {"spi:groupname": "DOA11_NAVY_PO", "_rowstamp": "1853710052", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/6-2370247", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/6-2370247/maxgroup/0-229", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "PO DOA upto $500,000 - US NAVY (ALL SITES)", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RPQTExX05BVllfUE8-", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "1122461975", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 229, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2370247, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RPQTExX05BVllfUE8vV0FSRDM4MzU3MzY-"}, {"spi:groupname": "DOA4_ARMY_PO", "_rowstamp": "1853710053", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/7-2370248", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/7-2370248/maxgroup/0-219", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "PO DOA up to $500,000 - US ARMY ALL SITES", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RPQTRfQVJNWV9QTw--", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "1009709246", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 219, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2370248, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RPQTRfQVJNWV9QTy9XQVJEMzgzNTczNg--"}, {"spi:groupname": "PBOSUSERS", "_rowstamp": "2383627900", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/8-2374103", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0FSRDM4MzU3MzY-/user/0-1172174/groupuser/8-2374103/maxgroup/0-295", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "2236740099", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 295, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PBOS Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BCT1NVU0VSUw--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2374103, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BCT1NVU0VSUy9XQVJEMzgzNTczNg--"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:ud_type": "MOD", "spi:querywithsite": true, "spi:sidenav": 0, "spi:isconsultant": false, "spi:ud_type_description": "Modification", "spi:inactivesites": false, "spi:userid": "WARD3835736", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172174, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvV0FSRDM4MzU3MzY-", "spi:sysuser": false, "spi:defsite": "NSGBA", "spi:ud_ticket": "RITM0048716", "spi:status_description": "Active", "spi:type_description": "PROCUREMENT ALL SITES", "spi:type": "TYPE 8"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Cuba", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USNAVY", "spi:timezone_description": "Cuba (UTC-5)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "NSGBA", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "WARD3835736", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-20T10:41:14+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989298, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2383629988", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-", "spi:displayname": "<PERSON>, <PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "_rowstamp": "2383629986", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1487113709", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/0-2369631", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/0-2369631/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369631, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1NBV1kzODM1NzM0"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1487113711", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/1-2369629", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/1-2369629/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369629, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1NBV1kzODM1NzM0"}, {"spi:groupname": "NSGBAUSERS", "_rowstamp": "1487275155", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/2-2369644", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/2-2369644/maxgroup/0-117", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018775", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 117, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "NSGBA Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL05TR0JBVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369644, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL05TR0JBVVNFUlMvU0FXWTM4MzU3MzQ-"}, {"spi:groupname": "LGCAPUSERS", "_rowstamp": "1487275157", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/3-2369643", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/3-2369643/maxgroup/0-205", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "518113149", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 205, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LGCAP Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xHQ0FQVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369643, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xHQ0FQVVNFUlMvU0FXWTM4MzU3MzQ-"}, {"spi:groupname": "KWAJUSERS", "_rowstamp": "1487275158", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/4-2369642", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/4-2369642/maxgroup/0-165", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "287494999", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 165, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "KWAJ Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0tXQUpVU0VSUw--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369642, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0tXQUpVU0VSUy9TQVdZMzgzNTczNA--"}, {"spi:groupname": "PROC-BUYER", "_rowstamp": "1487341367", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/5-2369655", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/5-2369655/maxgroup/0-86", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "182061985", "spi:authallgls": false, "spi:sctemplateid": 27, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 86, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Procurement Buyers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BST0MtQlVZRVI-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369655, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BST0MtQlVZRVIvU0FXWTM4MzU3MzQ-"}, {"spi:groupname": "DOA9_NAVY_PO", "_rowstamp": "1487345091", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/6-2369656", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/6-2369656/maxgroup/0-223", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "PO DOA up to 100K - US NAVY (ALL SITES)", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RPQTlfTkFWWV9QTw--", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "1088500684", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 223, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369656, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RPQTlfTkFWWV9QTy9TQVdZMzgzNTczNA--"}, {"spi:groupname": "DOA5_ARMY_PO", "_rowstamp": "1487345093", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/7-2369657", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/7-2369657/maxgroup/0-222", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "PO DOA up to $100,000 - US ARMY ALL SITES", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RPQTVfQVJNWV9QTw--", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "1088499624", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 222, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369657, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RPQTVfQVJNWV9QTy9TQVdZMzgzNTczNA--"}, {"spi:groupname": "PBOSUSERS", "_rowstamp": "2383630587", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/8-2374104", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FXWTM4MzU3MzQ-/user/0-1172173/groupuser/8-2374104/maxgroup/0-295", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "2236740099", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 295, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PBOS Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BCT1NVU0VSUw--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2374104, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BCT1NVU0VSUy9TQVdZMzgzNTczNA--"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:ud_type": "MOD", "spi:querywithsite": true, "spi:sidenav": 0, "spi:isconsultant": false, "spi:ud_type_description": "Modification", "spi:inactivesites": false, "spi:userid": "SAWY3835734", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172173, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvU0FXWTM4MzU3MzQ-", "spi:sysuser": false, "spi:defsite": "NSGBA", "spi:ud_ticket": "RITM0048716", "spi:status_description": "Active", "spi:type_description": "PROCUREMENT ALL SITES", "spi:type": "TYPE 8"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Cuba", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USNAVY", "spi:timezone_description": "Cuba (UTC-5)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "NSGBA", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "SAWY3835734", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-20T10:41:14+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989299, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "NQ100", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2216233129", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-", "spi:displayname": "<PERSON>, <PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "JOHN3835528", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172175, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvSk9ITjM4MzU1Mjg-", "spi:sysuser": false, "spi:defsite": "IKWAJ", "_rowstamp": "2216233130", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1487113708", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/0-2369634", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/0-2369634/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369634, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0pPSE4zODM1NTI4"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1487113713", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/1-2369633", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/1-2369633/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369633, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0pPSE4zODM1NTI4"}, {"spi:groupname": "KWAJUSERS", "_rowstamp": "1487281575", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/2-2369646", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/2-2369646/maxgroup/0-165", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "287494999", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 165, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "KWAJ Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0tXQUpVU0VSUw--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369646, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0tXQUpVU0VSUy9KT0hOMzgzNTUyOA--"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "1487292997", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/3-2369647", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/3-2369647/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 2369647, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9KT0hOMzgzNTUyOA--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1487292999", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/4-2369648", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Sk9ITjM4MzU1Mjg-/user/0-1172175/groupuser/4-2369648/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369648, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvSk9ITjM4MzU1Mjg-"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:memo": "Admin activation by User moor382170", "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "IKWAJ", "spi:deviceclass_description": "Advanced", "spi:department": "KWA-Transport Motor Pool (TMP)", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "JOHN3835528", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-20T10:41:14+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989300, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "ALASAD", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "MAYE369683", "spi:caltype": "gregorian", "spi:plusggastestauth": false, "_rowstamp": "2363817690", "spi:firstname": "<PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:shiptoaddress": "11039", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:language": "EN", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--", "spi:displayname": "Mackenzie, Pamelah - V2X", "spi:plusgisolationauth": false, "spi:billtoaddress": "VECTRUS", "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "CMW-ALA", "_rowstamp": "2119062408", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1498573294", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/0-2369679", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/0-2369679/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369679, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL01BQ0szODI0OTA-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1498573296", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/1-2369676", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/1-2369676/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369676, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL01BQ0szODI0OTA-"}, {"spi:groupname": "LCVIRQUSERS", "_rowstamp": "1509012221", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/2-2369715", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/2-2369715/maxgroup/0-110", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018756", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 110, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Iraq Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVklSUVVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369715, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVklSUVVTRVJTL01BQ0szODI0OTA-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1509026809", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/3-2369716", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/3-2369716/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369716, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvTUFDSzM4MjQ5MA--"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "2119062410", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/4-2372265", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFDSzM4MjQ5MA--/user/0-1172178/groupuser/4-2372265/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 2372265, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9NQUNLMzgyNDkw"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:ud_type": "MOD", "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "LCVIRQ", "spi:isconsultant": false, "spi:ud_type_description": "Modification", "spi:inactivesites": false, "spi:userid": "MACK382490", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172178, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvTUFDSzM4MjQ5MA--", "spi:sysuser": false, "spi:defsite": "LCVIRQ", "spi:ud_ticket": "RITM0039543", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Food Services Supervisor", "spi:locale_description": "English", "spi:status_description": "Active", "spi:timezone": "Asia/Baghdad", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Baghdad (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVIRQ", "spi:deviceclass_description": "Advanced", "spi:department": "DFAC", "spi:languserupdated": true, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "MACK382490", "spi:wfmailelection": "PROCESS", "spi:caltype_description": "Gregorian Calendar", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-22T13:06:46+00:00", "spi:transemailelection_description": "Never Notify", "spi:locale": "en", "spi:usernotftype": "EMAIL", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:droppoint": "DFAC", "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989310, "spi:usernotftype_description": "Email", "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "ALASAD", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "DUZA369774", "spi:caltype": "gregorian", "spi:plusggastestauth": false, "_rowstamp": "2363817691", "spi:firstname": "<PERSON><PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:shiptoaddress": "11039", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON>", "spi:language": "EN", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-", "spi:displayname": "Kazi, Hafiz - No Export", "spi:plusgisolationauth": false, "spi:billtoaddress": "VECTRUS", "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "CMW-ALA", "_rowstamp": "1508860859", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1498573204", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/0-2369673", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/0-2369673/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369673, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0tBWkkzODM1NzIz"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1498573206", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/1-2369672", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/1-2369672/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369672, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0tBWkkzODM1NzIz"}, {"spi:groupname": "LCVIRQUSERS", "_rowstamp": "1508793963", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/2-2369697", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/2-2369697/maxgroup/0-110", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018756", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 110, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Iraq Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVklSUVVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369697, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVklSUVVTRVJTL0tBWkkzODM1NzIz"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1508860862", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/3-2369705", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/3-2369705/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369705, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvS0FaSTM4MzU3MjM-"}, {"spi:groupname": "PWD-TECHNICIAN", "_rowstamp": "1508860863", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/4-2369706", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0FaSTM4MzU3MjM-/user/0-1172179/groupuser/4-2369706/maxgroup/0-43", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419253", "spi:authallgls": false, "spi:sctemplateid": 6, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 43, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Technicians", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1URUNITklDSUFO", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 2369706, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1URUNITklDSUFOL0tBWkkzODM1NzIz"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "LCVIRQ", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "KAZI3835723", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172179, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvS0FaSTM4MzU3MjM-", "spi:sysuser": false, "spi:defsite": "LCVIRQ", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "<PERSON>", "spi:locale_description": "English", "spi:status_description": "Active", "spi:timezone": "Asia/Baghdad", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Baghdad (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVIRQ", "spi:deviceclass_description": "Advanced", "spi:department": "Carpentry", "spi:languserupdated": true, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "KAZI3835723", "spi:wfmailelection": "PROCESS", "spi:caltype_description": "Gregorian Calendar", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-22T13:06:46+00:00", "spi:transemailelection_description": "Never Notify", "spi:locale": "en", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:droppoint": "Carpentry", "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989311, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "ALASAD", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "DUZA369774", "spi:caltype": "gregorian", "spi:plusggastestauth": false, "_rowstamp": "2363817693", "spi:firstname": "<PERSON><PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:shiptoaddress": "11039", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON>", "spi:language": "EN", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-", "spi:displayname": "Pan<PERSON>, Ishwar - No Export", "spi:plusgisolationauth": false, "spi:billtoaddress": "VECTRUS", "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "CMW-ALA", "_rowstamp": "1508866091", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1498573292", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/0-2369683", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/0-2369683/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369683, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1BBTkMzODM1NzIy"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1498573301", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/1-2369682", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/1-2369682/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369682, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1BBTkMzODM1NzIy"}, {"spi:groupname": "LCVIRQUSERS", "_rowstamp": "1508794906", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/2-2369698", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/2-2369698/maxgroup/0-110", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018756", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 110, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Iraq Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVklSUVVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369698, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVklSUVVTRVJTL1BBTkMzODM1NzIy"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1508866093", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/3-2369707", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/3-2369707/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369707, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvUEFOQzM4MzU3MjI-"}, {"spi:groupname": "PWD-TECHNICIAN", "_rowstamp": "1508866094", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/4-2369708", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEFOQzM4MzU3MjI-/user/0-1172181/groupuser/4-2369708/maxgroup/0-43", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419253", "spi:authallgls": false, "spi:sctemplateid": 6, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 43, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Technicians", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1URUNITklDSUFO", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 2369708, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1URUNITklDSUFOL1BBTkMzODM1NzIy"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "LCVIRQ", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "PANC3835722", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172181, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUEFOQzM4MzU3MjI-", "spi:sysuser": false, "spi:defsite": "LCVIRQ", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "<PERSON>", "spi:locale_description": "English", "spi:status_description": "Active", "spi:timezone": "Asia/Baghdad", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Baghdad (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVIRQ", "spi:deviceclass_description": "Advanced", "spi:department": "Carpentry", "spi:languserupdated": true, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "PANC3835722", "spi:wfmailelection": "PROCESS", "spi:caltype_description": "Gregorian Calendar", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-22T13:06:47+00:00", "spi:transemailelection_description": "Never Notify", "spi:locale": "en", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:droppoint": "Carpentry", "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989314, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "ERBIL", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "CHUR384649", "spi:caltype": "gregorian", "spi:plusggastestauth": false, "_rowstamp": "2404454140", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:shiptoaddress": "11084", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Millsap", "spi:language": "EN", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-", "spi:displayname": "Millsap, Bruce - V2X", "spi:plusgisolationauth": false, "spi:billtoaddress": "VECTRUS", "spi:maxuser": [{"spi:status": "INACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "CMW-ERB", "_rowstamp": "2404454141", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1498573283", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183/groupuser/0-2369680", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183/groupuser/0-2369680/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369680, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL01JTEwzODMyMjUx"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1498573285", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183/groupuser/1-2369678", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183/groupuser/1-2369678/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369678, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL01JTEwzODMyMjUx"}, {"spi:groupname": "LCVIRQUSERS", "_rowstamp": "1508779162", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183/groupuser/2-2369695", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183/groupuser/2-2369695/maxgroup/0-110", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018756", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 110, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Iraq Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVklSUVVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369695, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVklSUVVTRVJTL01JTEwzODMyMjUx"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1508856676", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183/groupuser/3-2369704", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUlMTDM4MzIyNTE-/user/0-1172183/groupuser/3-2369704/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369704, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvTUlMTDM4MzIyNTE-"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "LCVIRQ", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "MILL3832251", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172183, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvTUlMTDM4MzIyNTE-", "spi:sysuser": false, "spi:defsite": "LCVIRQ", "spi:memo": "Admin activation by User moor382170", "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:locale_description": "English", "spi:status_description": "Active", "spi:timezone": "Asia/Baghdad", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Baghdad (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVIRQ", "spi:deviceclass_description": "Advanced", "spi:department": "Base Camp Services", "spi:languserupdated": true, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "MILL3832251", "spi:wfmailelection": "PROCESS", "spi:caltype_description": "Gregorian Calendar", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-22T13:06:47+00:00", "spi:transemailelection_description": "Never Notify", "spi:locale": "en", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:droppoint": "Base Camp Services", "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989313, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "ALASAD", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "DUZA369774", "spi:caltype": "gregorian", "spi:plusggastestauth": false, "_rowstamp": "2363817695", "spi:firstname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:shiptoaddress": "11039", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Venugopal", "spi:language": "EN", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-", "spi:displayname": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Sreejithlal 1 - No Export", "spi:plusgisolationauth": false, "spi:billtoaddress": "VECTRUS", "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "CMW-ALA", "_rowstamp": "1508910325", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1498573441", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/0-2369685", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/0-2369685/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369685, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1ZFTlUzODM1NzI2"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1498573442", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/1-2369684", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/1-2369684/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369684, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1ZFTlUzODM1NzI2"}, {"spi:groupname": "LCVIRQUSERS", "_rowstamp": "1508798958", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/2-2369702", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/2-2369702/maxgroup/0-110", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018756", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 110, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Iraq Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVklSUVVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369702, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVklSUVVTRVJTL1ZFTlUzODM1NzI2"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1508910327", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/3-2369713", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/3-2369713/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369713, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvVkVOVTM4MzU3MjY-"}, {"spi:groupname": "PWD-TECHNICIAN", "_rowstamp": "1508910328", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/4-2369714", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VkVOVTM4MzU3MjY-/user/0-1172184/groupuser/4-2369714/maxgroup/0-43", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419253", "spi:authallgls": false, "spi:sctemplateid": 6, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 43, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Technicians", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1URUNITklDSUFO", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 2369714, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1URUNITklDSUFOL1ZFTlUzODM1NzI2"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "LCVIRQ", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "VENU3835726", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172184, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvVkVOVTM4MzU3MjY-", "spi:sysuser": false, "spi:defsite": "LCVIRQ", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "<PERSON>", "spi:locale_description": "English", "spi:status_description": "Active", "spi:timezone": "Asia/Baghdad", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Baghdad (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVIRQ", "spi:deviceclass_description": "Advanced", "spi:department": "Carpentry", "spi:languserupdated": true, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "VENU3835726", "spi:wfmailelection": "PROCESS", "spi:caltype_description": "Gregorian Calendar", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-22T13:06:53+00:00", "spi:transemailelection_description": "Never Notify", "spi:locale": "en", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:droppoint": "Carpentry", "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989315, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "ALASAD", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "KNIG307852", "spi:caltype": "gregorian", "spi:plusggastestauth": false, "_rowstamp": "2404456282", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:shiptoaddress": "11039", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:language": "EN", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-", "spi:displayname": "<PERSON>, <PERSON>", "spi:plusgisolationauth": false, "spi:billtoaddress": "VECTRUS", "spi:maxuser": [{"spi:status": "INACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "CMW-ALA", "_rowstamp": "2404456283", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1508766172", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185/groupuser/0-2369694", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185/groupuser/0-2369694/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369694, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1JPU0EzODMzOTA1"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1508766173", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185/groupuser/1-2369693", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185/groupuser/1-2369693/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369693, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1JPU0EzODMzOTA1"}, {"spi:groupname": "LCVIRQUSERS", "_rowstamp": "1508795971", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185/groupuser/2-2369699", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185/groupuser/2-2369699/maxgroup/0-110", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018756", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 110, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Iraq Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVklSUVVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369699, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVklSUVVTRVJTL1JPU0EzODMzOTA1"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1508809119", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185/groupuser/3-2369703", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9TQTM4MzM5MDU-/user/0-1172185/groupuser/3-2369703/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369703, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvUk9TQTM4MzM5MDU-"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "LCVIRQ", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "ROSA3833905", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172185, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUk9TQTM4MzM5MDU-", "spi:sysuser": false, "spi:defsite": "LCVIRQ", "spi:memo": "Admin activation by User moor382170", "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:locale_description": "English", "spi:status_description": "Active", "spi:timezone": "Asia/Baghdad", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Baghdad (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVIRQ", "spi:deviceclass_description": "Advanced", "spi:department": "HR", "spi:languserupdated": true, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "ROSA3833905", "spi:wfmailelection": "PROCESS", "spi:caltype_description": "Gregorian Calendar", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-24T11:22:24+00:00", "spi:transemailelection_description": "Never Notify", "spi:locale": "en", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:droppoint": "HR", "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989325, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2374244865", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "NON-Employee", "spi:ud_subcontractor_description": "LCVKWT - GSCS - LOGCAP", "spi:ud_subcontractor": "LCVKWT - GSCS - LOGCAP", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-", "spi:displayname": "<PERSON>, <PERSON>.", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "CRUZLSD-L13", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172190, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvQ1JVWkxTRC1MMTM-", "spi:sysuser": false, "spi:defsite": "LCVKWT", "_rowstamp": "2404451994", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1515813459", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190/groupuser/0-2369752", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190/groupuser/0-2369752/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369752, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0NSVVpMU0QtTDEz"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1515813461", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190/groupuser/1-2369751", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190/groupuser/1-2369751/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369751, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0NSVVpMU0QtTDEz"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "1515854059", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190/groupuser/2-2369759", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190/groupuser/2-2369759/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369759, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL0NSVVpMU0QtTDEz"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1515854060", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190/groupuser/3-2369758", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Q1JVWkxTRC1MMTM-/user/0-1172190/groupuser/3-2369758/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369758, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvQ1JVWkxTRC1MMTM-"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:memo": "Admin activation by User moor382170", "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:deviceclass": 2, "spi:employeetype": "NON-EMPL", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "CRUZLSD-L13", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-27T15:15:31+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989350, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "1515852610", "spi:firstname": "<PERSON><PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Barrameda", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-", "spi:displayname": "Barrameda, Hermenio", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "BARRLSD-L19", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 1172189, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvQkFSUkxTRC1MMTk-", "spi:sysuser": false, "spi:defsite": "LCVKWT", "_rowstamp": "2177286698", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "1515813460", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189/groupuser/0-2369753", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189/groupuser/0-2369753/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369753, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0JBUlJMU0QtTDE5"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "1515813462", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189/groupuser/1-2369750", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189/groupuser/1-2369750/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2369750, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0JBUlJMU0QtTDE5"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "1515852611", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189/groupuser/2-2369755", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189/groupuser/2-2369755/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369755, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvQkFSUkxTRC1MMTk-"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "1515852612", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189/groupuser/3-2369754", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFSUkxTRC1MMTk-/user/0-1172189/groupuser/3-2369754/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2369754, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL0JBUlJMU0QtTDE5"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:memo": "Admin activation by User moor382170", "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "BARRLSD-L19", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2024-06-27T15:15:31+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 989351, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:status": "ACTIVE", "spi:locationorg": "USNAVY", "spi:transemailelection": "ALWAYS", "spi:plusgmocrevapp": false, "spi:wfmailelection_description": "Always notify", "spi:locationsite": "NSGBA", "spi:plusgsolnapprover": false, "spi:languserupdated": false, "spi:plusgfinauthority": false, "_rowstamp": "2034613296", "spi:firstname": "maxadmin", "spi:plusgtechauthority": false, "spi:personid": "MAXADMIN", "spi:wfmailelection": "ALWAYS", "spi:plusgperissueauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2004-04-14T11:58:32+00:00", "spi:transemailelection_description": "Always Notify", "spi:plusgauditapprover": false, "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgisospec": false, "spi:plusgptwissuecounter": 0, "spi:loctoservreq": true, "spi:personuid": 3, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYQURNSU4-", "spi:displayname": "SVC-MAXAdmin2", "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYQURNSU4-/user/0-1", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": true, "spi:userid": "MAXADMIN", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 24, "spi:loginid": "<EMAIL>", "spi:maxuserid": 1, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvTUFYQURNSU4-", "spi:sysuser": true, "spi:defsite": "NSGBA", "_rowstamp": "2419778261", "spi:groupuser": [{"spi:groupname": "MAXADMIN", "_rowstamp": "13263", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYQURNSU4-/user/0-1/groupuser/0-3", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYQURNSU4-/user/0-1/groupuser/0-3/maxgroup/0-4", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "Maximo Administrators (Super Users)", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEFETUlO", "spi:pluspauthperslst": false, "_rowstamp": "2397601124", "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:authallgls": true, "spi:sctemplateid": 2, "spi:independent": true, "spi:authlaborall": true, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": true, "spi:maxgroupid": 4, "spi:pluspauthnoncust": false, "spi:authallrepfacs": true}], "spi:groupuserid": 3, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEFETUlOL01BWEFETUlO"}, {"spi:groupname": "MAXEVERYONE", "_rowstamp": "13266", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYQURNSU4-/user/0-1/groupuser/1-6", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYQURNSU4-/user/0-1/groupuser/1-6/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 6, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL01BWEFETUlO"}, {"spi:groupname": "MAXMOB", "_rowstamp": "2035047858", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYQURNSU4-/user/0-1/groupuser/2-2371614", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYQURNSU4-/user/0-1/groupuser/2-2371614/maxgroup/0-289", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "2035040970", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 289, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Maximo Mobile Group", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWE1PQg--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2371614, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWE1PQi9NQVhBRE1JTg--"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "IBM Users", "spi:type": "TYPE 9"}], "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:status_description": "Active", "spi:primaryemail": "<EMAIL>"}, {"spi:status": "ACTIVE", "spi:locationorg": "USNAVY", "spi:transemailelection": "ALWAYS", "spi:plusgmocrevapp": false, "spi:locationsite": "NSGBA", "spi:plusgsolnapprover": false, "spi:languserupdated": false, "spi:plusgfinauthority": false, "_rowstamp": "2047206341", "spi:firstname": "mxintadm", "spi:plusgtechauthority": false, "spi:personid": "MXINTADM", "spi:plusgperissueauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2004-12-01T13:15:31+00:00", "spi:transemailelection_description": "Always Notify", "spi:plusgauditapprover": false, "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgisospec": false, "spi:plusgptwissuecounter": 0, "spi:loctoservreq": true, "spi:personuid": 6, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TVhJTlRBRE0-", "spi:displayname": "SVC-MXIntAdm2", "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TVhJTlRBRE0-/user/0-2", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "MXINTADM", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 2971, "spi:loginid": "<EMAIL>", "spi:maxuserid": 2, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvTVhJTlRBRE0-", "spi:sysuser": true, "spi:defsite": "NSGBA", "_rowstamp": "2419777263", "spi:groupuser": [{"spi:groupname": "MAXADMIN", "_rowstamp": "13264", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TVhJTlRBRE0-/user/0-2/groupuser/0-4", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TVhJTlRBRE0-/user/0-2/groupuser/0-4/maxgroup/0-4", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "Maximo Administrators (Super Users)", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEFETUlO", "spi:pluspauthperslst": false, "_rowstamp": "2397601124", "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:authallgls": true, "spi:sctemplateid": 2, "spi:independent": true, "spi:authlaborall": true, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": true, "spi:maxgroupid": 4, "spi:pluspauthnoncust": false, "spi:authallrepfacs": true}], "spi:groupuserid": 4, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEFETUlOL01YSU5UQURN"}, {"spi:groupname": "MAXEVERYONE", "_rowstamp": "13267", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TVhJTlRBRE0-/user/0-2/groupuser/1-7", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TVhJTlRBRE0-/user/0-2/groupuser/1-7/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 7, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL01YSU5UQURN"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "IBM Users", "spi:type": "TYPE 9"}], "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:status_description": "Active"}, {"spi:status": "ACTIVE", "spi:transemailelection": "ALWAYS", "spi:plusgmocrevapp": false, "spi:plusgsolnapprover": false, "spi:languserupdated": false, "spi:plusgfinauthority": false, "_rowstamp": "1449206733", "spi:firstname": "maxreg", "spi:plusgtechauthority": false, "spi:personid": "MAXREG", "spi:plusgperissueauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2004-12-01T13:15:31+00:00", "spi:transemailelection_description": "Always Notify", "spi:plusgauditapprover": false, "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgisospec": false, "spi:plusgptwissuecounter": 0, "spi:loctoservreq": true, "spi:personuid": 7, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYUkVH", "spi:displayname": "SVC-MAXReg2", "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYUkVH/user/0-3", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "MAXREG", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 3, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvTUFYUkVH", "spi:sysuser": true, "_rowstamp": "2419777264", "spi:groupuser": [{"spi:groupname": "MAXREG", "_rowstamp": "13265", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYUkVH/user/0-3/groupuser/0-5", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYUkVH/user/0-3/groupuser/0-5/maxgroup/0-5", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "Self Registration Process Access", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWFJFRw--", "spi:pluspauthperslst": false, "_rowstamp": "789058", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": true, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 5, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 5, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWFJFRy9NQVhSRUc-"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "IBM Users", "spi:type": "TYPE 9"}], "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:status_description": "Active"}, {"spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusggastestauth": false, "_rowstamp": "12093914", "spi:firstname": "MAXIMO", "spi:plusgtechauthority": false, "spi:plusghfapprover": false, "spi:personid": "MAXIMO", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-21T16:14:23+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauditapprover": false, "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgptwissuecounter": 0, "spi:plusgcertrevapp": false, "spi:loctoservreq": true, "spi:personuid": 9, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYSU1P", "spi:displayname": "MAXIMO", "spi:plusgperareaauth": false, "spi:plusgisolationauth": false, "spi:plusgiscertified": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYSU1P/user/0-4", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": true, "spi:userid": "MAXIMO", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 1, "spi:loginid": "<EMAIL>", "spi:maxuserid": 4, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvTUFYSU1P", "spi:sysuser": true, "spi:defstoreroom": "LCVK-CMW-CAS", "spi:defsite": "LCVKWT", "_rowstamp": "2419777265", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "2444778", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYSU1P/user/0-4/groupuser/0-11", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYSU1P/user/0-4/groupuser/0-11/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 11, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL01BWElNTw--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "2444779", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYSU1P/user/0-4/groupuser/1-10", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYSU1P/user/0-4/groupuser/1-10/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 10, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL01BWElNTw--"}, {"spi:groupname": "MAXADMIN", "_rowstamp": "2444780", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYSU1P/user/0-4/groupuser/2-12", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TUFYSU1P/user/0-4/groupuser/2-12/maxgroup/0-4", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "Maximo Administrators (Super Users)", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEFETUlO", "spi:pluspauthperslst": false, "_rowstamp": "2397601124", "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:authallgls": true, "spi:sctemplateid": 2, "spi:independent": true, "spi:authlaborall": true, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": true, "spi:maxgroupid": 4, "spi:pluspauthnoncust": false, "spi:authallrepfacs": true}], "spi:groupuserid": 12, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEFETUlOL01BWElNTw--"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "IBM Users", "spi:type": "TYPE 9"}], "spi:plusgsitecheckerauth": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:status_description": "Active"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2367409918", "spi:firstname": "Corazon", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "904-5938978", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--", "spi:displayname": "Adalla, Corazon - Vectrus - No Export", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "SR-FAC-AV59", "_rowstamp": "2419777266", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3263563", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/0-42", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/0-42/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 42, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0FEQUwxMTAzODY-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3263564", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/1-41", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/1-41/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 41, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0FEQUwxMTAzODY-"}, {"spi:groupname": "PMO-MGR", "_rowstamp": "76781135", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/2-4726", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/2-4726/maxgroup/0-38", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495003", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 38, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PMO Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BNTy1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 4726, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BNTy1NR1IvQURBTDExMDM4Ng--"}, {"spi:groupname": "IT-SUP-LEAD", "_rowstamp": "76742177", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/3-4722", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/3-4722/maxgroup/0-52", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419411", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 52, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "IT  Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0lULVNVUC1MRUFE", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 4722, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0lULVNVUC1MRUFEL0FEQUwxMTAzODY-"}, {"spi:groupname": "PROP-SPECIALIST", "_rowstamp": "76789397", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/4-4847", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/4-4847/maxgroup/0-68", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "12213675", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 68, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Property Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BST1AtU1BFQ0lBTElTVA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 4847, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BST1AtU1BFQ0lBTElTVC9BREFMMTEwMzg2"}, {"spi:groupname": "WCMO-SPECIALIST", "_rowstamp": "76821192", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/5-5011", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/5-5011/maxgroup/0-36", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "24350265", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 36, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1BFQ0lBTElTVA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 5011, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1BFQ0lBTElTVC9BREFMMTEwMzg2"}, {"spi:groupname": "NSGBAUSERS", "_rowstamp": "77573164", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/6-5040", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/6-5040/maxgroup/0-117", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018775", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 117, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "NSGBA Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL05TR0JBVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 5040, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL05TR0JBVVNFUlMvQURBTDExMDM4Ng--"}, {"spi:groupname": "WCMO-SUP-LEAD", "_rowstamp": "77633890", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/7-5798", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/7-5798/maxgroup/0-35", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "2215874214", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 35, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 5798, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1VQLUxFQUQvQURBTDExMDM4Ng--"}, {"spi:groupname": "HR-SPECIALIST", "_rowstamp": "188487373", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/8-16126", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/8-16126/maxgroup/0-98", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494801", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 98, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "HR Specialists", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0hSLVNQRUNJQUxJU1Q-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 16126, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0hSLVNQRUNJQUxJU1QvQURBTDExMDM4Ng--"}, {"spi:groupname": "WHSE-Specialist", "_rowstamp": "223454259", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/9-17598", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/9-17598/maxgroup/0-57", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "193353218", "spi:authallgls": false, "spi:sctemplateid": 39, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 57, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Warehouse Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0UtU3BlY2lhbGlzdA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 17598, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0UtU3BlY2lhbGlzdC9BREFMMTEwMzg2"}, {"spi:groupname": "DTR-APPROVER", "_rowstamp": "634380239", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/10-777553", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/10-777553/maxgroup/0-137", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Approver", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1BUFBST1ZFUg--", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "106119764", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 137, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 777553, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1BUFBST1ZFUi9BREFMMTEwMzg2"}, {"spi:groupname": "DS-PASSWORD", "_rowstamp": "691377977", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/11-919128", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/11-919128/maxgroup/0-215", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "DataSplice password reset group", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RTLVBBU1NXT1JE", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "691256748", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 215, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 919128, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RTLVBBU1NXT1JEL0FEQUwxMTAzODY-"}, {"spi:groupname": "WOT_LOAD", "_rowstamp": "815127215", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/12-1228420", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/12-1228420/maxgroup/0-166", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "Work order import group", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dPVF9MT0FE", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "289984907", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 166, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 1228420, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dPVF9MT0FEL0FEQUwxMTAzODY-"}, {"spi:groupname": "WHSE_SHIPMENT", "_rowstamp": "2388925785", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/13-2374175", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/13-2374175/maxgroup/0-177", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "WHSE Shipment receiving", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0VfU0hJUE1FTlQ-", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "406092398", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 177, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2374175, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0VfU0hJUE1FTlQvQURBTDExMDM4Ng--"}, {"spi:groupname": "WHSE-SUP-LEAD", "_rowstamp": "2388925787", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/14-2374174", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/14-2374174/maxgroup/0-56", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495490", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 56, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Warehouse  Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0UtU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2374174, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0UtU1VQLUxFQUQvQURBTDExMDM4Ng--"}, {"spi:groupname": "WHSE-MGR", "_rowstamp": "2388925789", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/15-2374173", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QURBTDExMDM4Ng--/user/0-17/groupuser/15-2374173/maxgroup/0-55", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495479", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 55, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Warehouse  Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0UtTUdS", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2374173, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0UtTUdSL0FEQUwxMTAzODY-"}], "spi:screenreader": false, "spi:ud_type": "MOD", "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "NSGBA", "spi:isconsultant": false, "spi:ud_type_description": "Modification", "spi:inactivesites": false, "spi:userid": "ADAL110386", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 17, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvQURBTDExMDM4Ng--", "spi:sysuser": false, "spi:defsite": "NSGBA", "spi:ud_ticket": "RITM0049077", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Production Control Manager", "spi:status_description": "Active", "spi:timezone": "US/Eastern", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "KW", "spi:locationorg": "USNAVY", "spi:timezone_description": "US/Eastern (UTC-5)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "NSGBA", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:personid": "ADAL110386", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2021-07-06T09:48:00+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 21, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2363817709", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:city": "APO", "spi:employeetype_description": "Company Employee", "spi:primaryphone": "************", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Soto", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--", "spi:displayname": "Soto, Raymond - <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "_rowstamp": "2419777267", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3387463", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/0-49", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/0-49/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 49, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1NPVE8xMTEzMTM-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3387464", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/1-48", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/1-48/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 48, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1NPVE8xMTEzMTM-"}, {"spi:groupname": "PMO-DIR-SRMGR", "_rowstamp": "3406946", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/2-254", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/2-254/maxgroup/0-37", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494990", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 37, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PMO Sr Managers and Director", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BNTy1ESVItU1JNR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 254, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BNTy1ESVItU1JNR1IvU09UTzExMTMxMw--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3413830", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/3-396", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/3-396/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 396, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvU09UTzExMTMxMw--"}, {"spi:groupname": "NSGBAUSERS", "_rowstamp": "77573332", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/4-5043", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/4-5043/maxgroup/0-117", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018775", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 117, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "NSGBA Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL05TR0JBVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 5043, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL05TR0JBVVNFUlMvU09UTzExMTMxMw--"}, {"spi:groupname": "WCMO-SUP-LEAD", "_rowstamp": "77633892", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/5-5800", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/5-5800/maxgroup/0-35", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "2215874214", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 35, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 5800, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1VQLUxFQUQvU09UTzExMTMxMw--"}, {"spi:groupname": "DOA_USNAVY_PR", "_rowstamp": "625239953", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/6-751043", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U09UTzExMTMxMw--/user/0-20/groupuser/6-751043/maxgroup/0-209", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "PR DOA up to $500,000 - All Navy sites", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RPQV9VU05BVllfUFI-", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "625173473", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 209, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 751043, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RPQV9VU05BVllfUFIvU09UTzExMTMxMw--"}], "spi:screenreader": false, "spi:ud_type": "MOD", "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "NSGBA", "spi:isconsultant": false, "spi:ud_type_description": "Modification", "spi:inactivesites": false, "spi:userid": "SOTO111313", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 20, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvU09UTzExMTMxMw--", "spi:sysuser": false, "spi:defsite": "NSGBA", "spi:ud_ticket": "INC0160154", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Project Manager", "spi:stateprovince": "AE", "spi:status_description": "Active", "spi:timezone": "US/Eastern", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USNAVY", "spi:timezone_description": "US/Eastern (UTC-5)", "spi:transemailelection": "NEVER", "spi:addressline1": "QBOSS PMO Bldg #9032, Camp As Sayliyah", "spi:plusgmocrevapp": false, "spi:locationsite": "NSGBA", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:postalcode": "09898", "spi:personid": "SOTO111313", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T12:53:14+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 23, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "Raymond<PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "SOTO111313", "spi:plusggastestauth": false, "_rowstamp": "2363817710", "spi:firstname": "<PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:city": "Virginia", "spi:employeetype_description": "Company Employee", "spi:primaryphone": "****** 220 5689", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--", "spi:displayname": "<PERSON>, <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "NSGBA", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "WILS106838", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 21, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvV0lMUzEwNjgzOA--", "spi:sysuser": false, "spi:defsite": "NSGBA", "_rowstamp": "1449280499", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3387693", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/0-51", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/0-51/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 51, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1dJTFMxMDY4Mzg-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3387694", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/1-50", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/1-50/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 50, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1dJTFMxMDY4Mzg-"}, {"spi:groupname": "PMO-DIR-SRMGR", "_rowstamp": "3406947", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/2-255", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/2-255/maxgroup/0-37", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494990", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 37, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PMO Sr Managers and Director", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BNTy1ESVItU1JNR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 255, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BNTy1ESVItU1JNR1IvV0lMUzEwNjgzOA--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3413831", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/3-397", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/3-397/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 397, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvV0lMUzEwNjgzOA--"}, {"spi:groupname": "PMO-MGR", "_rowstamp": "76781143", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/4-4732", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/4-4732/maxgroup/0-38", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495003", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 38, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PMO Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BNTy1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 4732, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BNTy1NR1IvV0lMUzEwNjgzOA--"}, {"spi:groupname": "NSGBAUSERS", "_rowstamp": "77581636", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/5-5795", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_V0lMUzEwNjgzOA--/user/0-21/groupuser/5-5795/maxgroup/0-117", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018775", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 117, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "NSGBA Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL05TR0JBVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 5795, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL05TR0JBVVNFUlMvV0lMUzEwNjgzOA--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Quality Control Manager", "spi:stateprovince": "<PERSON>", "spi:status_description": "Active", "spi:timezone": "US/Eastern", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "US", "spi:locationorg": "USNAVY", "spi:timezone_description": "US/Eastern (UTC-5)", "spi:transemailelection": "NEVER", "spi:addressline1": "7901 Jones Branch Drive\r\nSuite 700", "spi:plusgmocrevapp": false, "spi:locationsite": "NSGBA", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:postalcode": "22102", "spi:personid": "WILS106838", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T12:55:09+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 24, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "Kris<PERSON><PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2377636334", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-3129-1929", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--", "spi:displayname": "<PERSON><PERSON><PERSON>, Kim -Vectrus -QBOSS", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "KIM.KOVALESKI", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 23, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvS0lNLktPVkFMRVNLSQ--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280500", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3388165", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23/groupuser/0-55", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23/groupuser/0-55/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 55, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0tJTS5LT1ZBTEVTS0k-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3388166", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23/groupuser/1-54", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23/groupuser/1-54/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 54, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0tJTS5LT1ZBTEVTS0k-"}, {"spi:groupname": "ESH-MGR", "_rowstamp": "3405742", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23/groupuser/2-226", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23/groupuser/2-226/maxgroup/0-72", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494665", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 72, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "ESH  Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0VTSC1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 226, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0VTSC1NR1IvS0lNLktPVkFMRVNLSQ--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411611", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23/groupuser/3-321", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_S0lNLktPVkFMRVNLSQ--/user/0-23/groupuser/3-321/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 321, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvS0lNLktPVkFMRVNLSQ--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Environmental Compliance Manag", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "KIM.KOVALESKI", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T12:59:32+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 26, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON><PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "232098071", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:primaryphone": "+974-7752-6492", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Taylor1", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--", "spi:displayname": "<PERSON><PERSON>, <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "TAYL124084", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 24, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvVEFZTDEyNDA4NA--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280501", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3388323", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24/groupuser/0-57", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24/groupuser/0-57/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 57, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1RBWUwxMjQwODQ-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3388324", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24/groupuser/1-56", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24/groupuser/1-56/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 56, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1RBWUwxMjQwODQ-"}, {"spi:groupname": "ESH-SUP-LEAD", "_rowstamp": "3405876", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24/groupuser/2-230", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24/groupuser/2-230/maxgroup/0-73", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419352", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 73, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "ESH Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0VTSC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 230, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0VTSC1TVVAtTEVBRC9UQVlMMTI0MDg0"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411613", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24/groupuser/3-322", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VEFZTDEyNDA4NA--/user/0-24/groupuser/3-322/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 322, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvVEFZTDEyNDA4NA--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:stateprovince": "NY", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:country": "US", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:personid": "TAYL124084", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:00:54+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 27, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "Michael<PERSON><EMAIL>"}, {"spi:location": "ERBIL", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "VARE124073", "spi:caltype": "gregorian", "spi:plusggastestauth": false, "_rowstamp": "2363817712", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:city": "APO", "spi:employeetype_description": "Company Employee", "spi:primaryphone": "432-2761", "spi:shiptoaddress": "11084", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Hill", "spi:language": "EN", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM", "spi:displayname": "<PERSON>, Anthony - <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:billtoaddress": "VECTRUS", "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 1, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVIRQ", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "ANTHONY.HILL", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 27, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvQU5USE9OWS5ISUxM", "spi:sysuser": false, "spi:defstoreroom": "CMW-ALA", "spi:defsite": "LCVIRQ", "_rowstamp": "2419777268", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3388603", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/0-63", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/0-63/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 63, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0FOVEhPTlkuSElMTA--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3388604", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/1-62", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/1-62/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 62, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0FOVEhPTlkuSElMTA--"}, {"spi:groupname": "LCVIRQUSERS", "_rowstamp": "56016933", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/2-2736", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/2-2736/maxgroup/0-110", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018756", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 110, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Iraq Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVklSUVVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2736, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVklSUVVTRVJTL0FOVEhPTlkuSElMTA--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "915518700", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/3-1478863", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/3-1478863/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 1478863, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvQU5USE9OWS5ISUxM"}, {"spi:groupname": "FIRE-SUP-LEAD", "_rowstamp": "915518701", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/4-1478864", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/4-1478864/maxgroup/0-81", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419381", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 81, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Fire Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0ZJUkUtU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1478864, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0ZJUkUtU1VQLUxFQUQvQU5USE9OWS5ISUxM"}, {"spi:groupname": "FIRE-MGR", "_rowstamp": "915518702", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/5-1478865", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/5-1478865/maxgroup/0-80", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494770", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 80, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Fire Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0ZJUkUtTUdS", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1478865, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0ZJUkUtTUdSL0FOVEhPTlkuSElMTA--"}, {"spi:groupname": "FIRE-FIGHTERS", "_rowstamp": "915518703", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/6-1478866", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/6-1478866/maxgroup/0-82", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3418680", "spi:authallgls": false, "spi:sctemplateid": 6, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 82, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Fire Specialist and Alarm Technicians", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0ZJUkUtRklHSFRFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1478866, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0ZJUkUtRklHSFRFUlMvQU5USE9OWS5ISUxM"}, {"spi:groupname": "FIRE-DIR-SR<PERSON><PERSON>", "_rowstamp": "915518704", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/7-1478867", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU5USE9OWS5ISUxM/user/0-27/groupuser/7-1478867/maxgroup/0-79", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494744", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 79, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Fire Sr Managers, Director and Chief", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0ZJUkUtRElSLVNSTUdS", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1478867, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0ZJUkUtRElSLVNSTUdSL0FOVEhPTlkuSElMTA--"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "DTOM", "spi:stateprovince": "AE", "spi:locale_description": "English", "spi:status_description": "Active", "spi:timezone": "Asia/Baghdad", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Baghdad (UTC+3)", "spi:transemailelection": "NEVER", "spi:addressline1": "Vectrus Fire Department", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVIRQ", "spi:deviceclass_description": "Advanced", "spi:department": "DTOM", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:postalcode": "09898", "spi:personid": "ANTHONY.HILL", "spi:wfmailelection": "PROCESS", "spi:caltype_description": "Gregorian Calendar", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:03:29+00:00", "spi:transemailelection_description": "Never Notify", "spi:locale": "en", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:droppoint": "DTOM", "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 29, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "Anthony<PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "GARC124093", "spi:plusggastestauth": false, "_rowstamp": "2363817713", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-3324-8732", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM", "spi:displayname": "<PERSON>, <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> -QBOSS", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "JUAN.EMANUEL", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 28, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvSlVBTi5FTUFOVUVM", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280504", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3388754", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28/groupuser/0-65", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28/groupuser/0-65/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 65, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0pVQU4uRU1BTlVFTA--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3388755", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28/groupuser/1-64", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28/groupuser/1-64/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 64, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0pVQU4uRU1BTlVFTA--"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "3408675", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28/groupuser/2-318", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28/groupuser/2-318/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 318, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9KVUFOLkVNQU5VRUw-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "37279215", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28/groupuser/3-1308", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SlVBTi5FTUFOVUVM/user/0-28/groupuser/3-1308/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 1308, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvSlVBTi5FTUFOVUVM"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "DFAC Supervisor", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "JUAN.EMANUEL", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:04:42+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 30, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "Juan<PERSON>@gov2x.com"}, {"spi:location": "CAS-1025", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "GARC124093", "spi:plusggastestauth": false, "_rowstamp": "2363817714", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--", "spi:displayname": "<PERSON>, <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "INACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "LCVK-CMW-CAS", "_rowstamp": "2317420948", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3388933", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29/groupuser/0-67", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29/groupuser/0-67/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 67, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0VSSUMuU0xBREU-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3388934", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29/groupuser/1-66", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29/groupuser/1-66/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 66, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0VSSUMuU0xBREU-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411618", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29/groupuser/2-325", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29/groupuser/2-325/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 325, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvRVJJQy5TTEFERQ--"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "229486800", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29/groupuser/3-18200", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RVJJQy5TTEFERQ--/user/0-29/groupuser/3-18200/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 18200, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL0VSSUMuU0xBREU-"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:ud_type": "MOD", "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:ud_type_description": "Modification", "spi:inactivesites": false, "spi:userid": "ERIC.SLADE", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 29, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvRVJJQy5TTEFERQ--", "spi:sysuser": false, "spi:defsite": "LCVKWT", "spi:ud_ticket": "RITM0044348", "spi:memo": "Admin activation by User moor382170", "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "ERIC.SLADE", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:06:00+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 31, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "WILS106838", "spi:plusggastestauth": false, "_rowstamp": "2363817715", "spi:firstname": "Guadalupe", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-3007-1981", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--", "spi:displayname": "Garcia, Guadalupe - <PERSON>ectr<PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "GARC124093", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 30, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR0FSQzEyNDA5Mw--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280506", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3389082", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/0-69", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/0-69/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 69, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0dBUkMxMjQwOTM-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3389083", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/1-68", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/1-68/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 68, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0dBUkMxMjQwOTM-"}, {"spi:groupname": "PWD-MGR", "_rowstamp": "3407208", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/2-259", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/2-259/maxgroup/0-41", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495211", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 41, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 259, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1NR1IvR0FSQzEyNDA5Mw--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411620", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/3-326", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/3-326/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 326, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvR0FSQzEyNDA5Mw--"}, {"spi:groupname": "WCMO-SPECIALIST", "_rowstamp": "5472728", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/4-443", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FSQzEyNDA5Mw--/user/0-30/groupuser/4-443/maxgroup/0-36", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "24350265", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 36, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1BFQ0lBTElTVA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 443, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1BFQ0lBTElTVC9HQVJDMTI0MDkz"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Site Manager", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "GARC124093", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:07:44+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 32, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2363817716", "spi:firstname": "<PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-3129-1991", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP", "spi:displayname": "Acevedo, Elvi -Vectrus -QBOSS", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "ELVI.ACEVEDO", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 31, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvRUxWSS5BQ0VWRURP", "spi:sysuser": false, "spi:defstoreroom": "LCVK-CMW-CAS", "spi:defsite": "LCVKWT", "_rowstamp": "1449280507", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3389247", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/0-71", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/0-71/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 71, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0VMVkkuQUNFVkVETw--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3389248", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/1-70", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/1-70/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 70, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0VMVkkuQUNFVkVETw--"}, {"spi:groupname": "PWD-MGR", "_rowstamp": "3407209", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/2-260", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/2-260/maxgroup/0-41", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495211", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 41, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 260, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1NR1IvRUxWSS5BQ0VWRURP"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411622", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/3-327", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/3-327/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 327, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvRUxWSS5BQ0VWRURP"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "229486802", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/4-18198", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RUxWSS5BQ0VWRURP/user/0-31/groupuser/4-18198/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 18198, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL0VMVkkuQUNFVkVETw--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Food Services Manager", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:personid": "ELVI.ACEVEDO", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:09:16+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 33, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "ROBERT.ROQUE", "spi:plusggastestauth": false, "_rowstamp": "2363817717", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-5056-7417", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT", "spi:displayname": "<PERSON>, <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "AMANDA.DAVIS", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<PERSON><PERSON>@vectrus.com", "spi:maxuserid": 32, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvQU1BTkRBLkRBVklT", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280508", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3389529", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/0-73", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/0-73/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 73, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0FNQU5EQS5EQVZJUw--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3389530", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/1-72", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/1-72/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 72, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0FNQU5EQS5EQVZJUw--"}, {"spi:groupname": "WCMO-SUP-LEAD", "_rowstamp": "3408081", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/2-294", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/2-294/maxgroup/0-35", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "2215874214", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 35, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 294, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1VQLUxFQUQvQU1BTkRBLkRBVklT"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411624", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/3-328", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/3-328/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 328, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvQU1BTkRBLkRBVklT"}, {"spi:groupname": "HR-SPECIALIST", "_rowstamp": "5446087", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/4-439", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/4-439/maxgroup/0-98", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494801", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 98, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "HR Specialists", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0hSLVNQRUNJQUxJU1Q-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 439, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0hSLVNQRUNJQUxJU1QvQU1BTkRBLkRBVklT"}, {"spi:groupname": "PROP-MGR", "_rowstamp": "45022152", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/5-1546", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QU1BTkRBLkRBVklT/user/0-32/groupuser/5-1546/maxgroup/0-66", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "12213668", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 66, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Property Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BST1AtTUdS", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 1546, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BST1AtTUdSL0FNQU5EQS5EQVZJUw--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Property Manager", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "AMANDA.DAVIS", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:11:25+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 34, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "WILS106838", "spi:plusggastestauth": false, "_rowstamp": "2363817719", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:city": "APO", "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974.3358.9382", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS", "spi:displayname": "<PERSON>, <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "DEVIN.BUTLER", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 35, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvREVWSU4uQlVUTEVS", "spi:sysuser": false, "spi:defsite": "IKWAJ", "_rowstamp": "2419777269", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3389968", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/0-79", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/0-79/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 79, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0RFVklOLkJVVExFUg--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3389969", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/1-78", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/1-78/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 78, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0RFVklOLkJVVExFUg--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411630", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/2-331", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/2-331/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 331, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvREVWSU4uQlVUTEVS"}, {"spi:groupname": "KWAJUSERS", "_rowstamp": "292180215", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/3-56023", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/3-56023/maxgroup/0-165", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "287494999", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 165, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "KWAJ Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0tXQUpVU0VSUw--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 56023, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0tXQUpVU0VSUy9ERVZJTi5CVVRMRVI-"}, {"spi:groupname": "PWD-DIR-SRMGR", "_rowstamp": "292180216", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/4-56024", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/4-56024/maxgroup/0-40", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495184", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 40, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Sr Managers and Director", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1ESVItU1JNR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 56024, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1ESVItU1JNR1IvREVWSU4uQlVUTEVS"}, {"spi:groupname": "PWD-MGR", "_rowstamp": "692197707", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/5-921314", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/5-921314/maxgroup/0-41", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495211", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 41, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 921314, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1NR1IvREVWSU4uQlVUTEVS"}, {"spi:groupname": "DOA6_USARMY_PR", "_rowstamp": "1985231437", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/6-2371229", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_REVWSU4uQlVUTEVS/user/0-35/groupuser/6-2371229/maxgroup/0-251", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "PR DOA upto $250,000 - All Army sites", "spi:authlaborcrew": false, "spi:passwordduration": 365, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RPQTZfVVNBUk1ZX1BS", "spi:pluspauthperslst": false, "spi:passwordwarning": 5, "_rowstamp": "1444914602", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 251, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 2371229, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RPQTZfVVNBUk1ZX1BSL0RFVklOLkJVVExFUg--"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "InstallationLogistics<PERSON><PERSON><PERSON>,", "spi:stateprovince": "AE", "spi:status_description": "Active", "spi:timezone": "Pacific/Fiji", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Pacific/Fiji (UTC+12)", "spi:transemailelection": "NEVER", "spi:addressline1": "Unit 526", "spi:plusgmocrevapp": false, "spi:locationsite": "IKWAJ", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:postalcode": "09898", "spi:personid": "DEVIN.BUTLER", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:15:42+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 37, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "600875327", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:city": "<PERSON>", "spi:primaryphone": "+974-3005-6394", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Grosso", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--", "spi:displayname": "Grosso, <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "GROS124094", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<PERSON>@vectrus.com", "spi:maxuserid": 36, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPUzEyNDA5NA--", "spi:sysuser": false, "spi:defstoreroom": "LCVK-CMW-CAS", "spi:defsite": "LCVKWT", "_rowstamp": "1449280512", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3390129", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/0-81", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/0-81/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 81, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0dST1MxMjQwOTQ-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3390130", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/1-80", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/1-80/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 80, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0dST1MxMjQwOTQ-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411632", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/2-332", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/2-332/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 332, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvR1JPUzEyNDA5NA--"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "165712463", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/3-15232", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/3-15232/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 15232, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9HUk9TMTI0MDk0"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "229486793", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/4-18207", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R1JPUzEyNDA5NA--/user/0-36/groupuser/4-18207/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 18207, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL0dST1MxMjQwOTQ-"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:stateprovince": "CA", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:country": "US", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:addressline1": "122 <PERSON> <PERSON>", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:postalcode": "95008", "spi:personid": "GROS124094", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:16:53+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 38, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON>@gov2x.com"}, {"spi:location": "CAS-1025", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "ELLISH", "spi:plusggastestauth": false, "_rowstamp": "2363817721", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-3129-1939", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-", "spi:displayname": "<PERSON>, <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "REMOVED", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "RONALD.LUCIANO", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 38, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUk9OQUxELkxVQ0lBTk8-", "spi:sysuser": false, "spi:defstoreroom": "LCVK-CMW-CAS", "spi:defsite": "LCVKWT", "_rowstamp": "1449280514", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3390543", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/0-85", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/0-85/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 85, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1JPTkFMRC5MVUNJQU5P"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3390544", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/1-84", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/1-84/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 84, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1JPTkFMRC5MVUNJQU5P"}, {"spi:groupname": "MWR-MGR", "_rowstamp": "3406668", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/2-248", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/2-248/maxgroup/0-59", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494960", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 59, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "MWR Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01XUi1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 248, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01XUi1NR1IvUk9OQUxELkxVQ0lBTk8-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411636", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/3-334", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/3-334/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 334, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvUk9OQUxELkxVQ0lBTk8-"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "229486766", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/4-18234", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELkxVQ0lBTk8-/user/0-38/groupuser/4-18234/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 18234, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL1JPTkFMRC5MVUNJQU5P"}], "spi:screenreader": false, "spi:status_description": "Removed due to no usage", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Shelter & Billeting Manager", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:department": "3400 - Military On-Post Housin", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:personid": "RONALD.LUCIANO", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:20:39+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 40, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2377674711", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O", "spi:displayname": "<PERSON>, <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "INACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "_rowstamp": "2377674712", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3390681", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/0-87", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/0-87/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 87, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0pBTUVTLk1DUEhFUlNPTg--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3390682", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/1-86", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/1-86/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 86, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0pBTUVTLk1DUEhFUlNPTg--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411638", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/2-335", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/2-335/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 335, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvSkFNRVMuTUNQSEVSU09O"}, {"spi:groupname": "NSGBAUSERS", "_rowstamp": "854967279", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/3-1326731", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/3-1326731/maxgroup/0-117", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018775", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 117, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "NSGBA Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL05TR0JBVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 1326731, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL05TR0JBVVNFUlMvSkFNRVMuTUNQSEVSU09O"}, {"spi:groupname": "PWD-MGR", "_rowstamp": "855145399", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/4-1327196", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/4-1327196/maxgroup/0-41", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495211", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 41, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1327196, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1NR1IvSkFNRVMuTUNQSEVSU09O"}, {"spi:groupname": "PMO-MGR", "_rowstamp": "855145400", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/5-1327197", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/5-1327197/maxgroup/0-38", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495003", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 38, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PMO Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BNTy1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1327197, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BNTy1NR1IvSkFNRVMuTUNQSEVSU09O"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "855145707", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/6-1327198", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_SkFNRVMuTUNQSEVSU09O/user/0-39/groupuser/6-1327198/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1327198, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9KQU1FUy5NQ1BIRVJTT04-"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "NSGBA", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "JAMES.MCPHERSON", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 39, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvSkFNRVMuTUNQSEVSU09O", "spi:sysuser": false, "spi:defsite": "NSGBA", "spi:memo": "Admin activation by User michael.allen", "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "US/Eastern", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USNAVY", "spi:timezone_description": "US/Eastern (UTC-5)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "NSGBA", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "JAMES.MCPHERSON", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:21:56+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 41, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "CAS-1025", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "SHINEKA.GEORGE", "spi:plusggastestauth": false, "_rowstamp": "2363817725", "spi:firstname": "<PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Plomines", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--", "spi:displayname": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> - V<PERSON>trus No Export", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "PLOM124064", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "Cris.<PERSON>Plo<PERSON>@vectrus.com", "spi:maxuserid": 42, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUExPTTEyNDA2NA--", "spi:sysuser": false, "spi:defstoreroom": "LCVK-CMW-CAS", "spi:defsite": "LCVKWT", "_rowstamp": "2419777270", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3391170", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/0-93", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/0-93/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 93, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1BMT00xMjQwNjQ-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3391171", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/1-92", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/1-92/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 92, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1BMT00xMjQwNjQ-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3413792", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/2-395", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/2-395/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 395, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvUExPTTEyNDA2NA--"}, {"spi:groupname": "IT-SUP-LEAD", "_rowstamp": "234098189", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/3-18805", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/3-18805/maxgroup/0-52", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419411", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 52, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "IT  Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0lULVNVUC1MRUFE", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 18805, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0lULVNVUC1MRUFEL1BMT00xMjQwNjQ-"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "229486773", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/4-18227", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/4-18227/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 18227, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL1BMT00xMjQwNjQ-"}, {"spi:groupname": "WCMO-SPECIALIST", "_rowstamp": "850201593", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/5-1315129", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UExPTTEyNDA2NA--/user/0-42/groupuser/5-1315129/maxgroup/0-36", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "24350265", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 36, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1BFQ0lBTElTVA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1315129, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1BFQ0lBTElTVC9QTE9NMTI0MDY0"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Systems Administrator", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:department": "1700 - IT Support Services (IT", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:personid": "PLOM124064", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:22:56+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 42, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "12087624", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:primaryphone": "+974-3138-5307", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--", "spi:displayname": "<PERSON><PERSON>, <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "SESA124091", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 41, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvU0VTQTEyNDA5MQ--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280517", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3391034", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41/groupuser/0-91", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41/groupuser/0-91/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 91, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1NFU0ExMjQwOTE-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3391035", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41/groupuser/1-90", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41/groupuser/1-90/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 90, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1NFU0ExMjQwOTE-"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "3407515", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41/groupuser/2-267", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41/groupuser/2-267/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 267, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9TRVNBMTI0MDkx"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411642", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41/groupuser/3-337", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0VTQTEyNDA5MQ--/user/0-41/groupuser/3-337/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 337, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvU0VTQTEyNDA5MQ--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "SESA124091", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:24:52+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 44, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "12087603", "spi:firstname": "<PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:city": "Greenville", "spi:primaryphone": "+974-3129-1990", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Flores", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--", "spi:displayname": "Flores, Jesusita - <PERSON>ectrus", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "FLOR108364", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 43, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvRkxPUjEwODM2NA--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280519", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3391192", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43/groupuser/0-95", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43/groupuser/0-95/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 95, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0ZMT1IxMDgzNjQ-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3391193", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43/groupuser/1-94", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43/groupuser/1-94/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 94, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0ZMT1IxMDgzNjQ-"}, {"spi:groupname": "MWR-SUP-LEAD", "_rowstamp": "3406751", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43/groupuser/2-249", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43/groupuser/2-249/maxgroup/0-60", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419432", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 60, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "MWR Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01XUi1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 249, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01XUi1TVVAtTEVBRC9GTE9SMTA4MzY0"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411644", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43/groupuser/3-338", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RkxPUjEwODM2NA--/user/0-43/groupuser/3-338/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 338, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvRkxPUjEwODM2NA--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:stateprovince": "SC", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:country": "US", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:addressline1": "2611 Keeler Run Court", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:postalcode": "29617", "spi:personid": "FLOR108364", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:26:28+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 45, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "12087530", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:primaryphone": "+974-3002-8964", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Brisbane", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--", "spi:displayname": "Brisbane, <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "BRIS120405", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "Christopher<PERSON>@vectrus.com", "spi:maxuserid": 44, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvQlJJUzEyMDQwNQ--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "*********0", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3391323", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44/groupuser/0-97", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44/groupuser/0-97/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 97, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0JSSVMxMjA0MDU-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3391324", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44/groupuser/1-96", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44/groupuser/1-96/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 96, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0JSSVMxMjA0MDU-"}, {"spi:groupname": "MWR-SUP-LEAD", "_rowstamp": "3406752", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44/groupuser/2-250", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44/groupuser/2-250/maxgroup/0-60", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419432", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 60, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "MWR Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01XUi1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 250, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01XUi1TVVAtTEVBRC9CUklTMTIwNDA1"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411646", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44/groupuser/3-339", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QlJJUzEyMDQwNQ--/user/0-44/groupuser/3-339/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 339, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvQlJJUzEyMDQwNQ--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "BRIS120405", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:28:08+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 46, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "Christopher<PERSON>@gov2x.com"}, {"spi:location": "CAS-1025", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2372332673", "spi:firstname": "<PERSON><PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:city": "NA", "spi:employeetype_description": "Company Employee", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Gaire", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--", "spi:displayname": "Gaire, Govinda - Vectrus - NoExport", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "GAIR125366", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 45, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR0FJUjEyNTM2Ng--", "spi:sysuser": false, "spi:defstoreroom": "LCVK-CMW-CAS", "spi:defsite": "LCVKWT", "_rowstamp": "2419777271", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3391400", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/0-99", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/0-99/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 99, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0dBSVIxMjUzNjY-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3391401", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/1-98", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/1-98/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 98, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0dBSVIxMjUzNjY-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3406239", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/2-241", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/2-241/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 241, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvR0FJUjEyNTM2Ng--"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "239500258", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/3-19344", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/3-19344/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 19344, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL0dBSVIxMjUzNjY-"}, {"spi:groupname": "WHSE-Specialist", "_rowstamp": "439007032", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/4-342118", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R0FJUjEyNTM2Ng--/user/0-45/groupuser/4-342118/maxgroup/0-57", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "193353218", "spi:authallgls": false, "spi:sctemplateid": 39, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 57, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Warehouse Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0UtU3BlY2lhbGlzdA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 342118, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0UtU3BlY2lhbGlzdC9HQUlSMTI1MzY2"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Administrative Assistant", "spi:stateprovince": "NA", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "IS", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:addressline1": "NA", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:department": "6500 - Full Food Service (FFS)", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:postalcode": "NA", "spi:personid": "GAIR125366", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:29:13+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 48, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2377636498", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-3129-1599", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Roque", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF", "spi:displayname": "<PERSON><PERSON><PERSON>, Robert -Vectrus -QBOSS", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "ROBERT.ROQUE", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 46, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUk9CRVJULlJPUVVF", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "*********2", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3391458", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/0-101", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/0-101/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 101, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1JPQkVSVC5ST1FVRQ--"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3391459", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/1-100", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/1-100/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 100, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1JPQkVSVC5ST1FVRQ--"}, {"spi:groupname": "PWD-MGR", "_rowstamp": "3407210", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/2-261", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/2-261/maxgroup/0-41", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495211", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 41, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 261, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1NR1IvUk9CRVJULlJPUVVF"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411648", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/3-340", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/3-340/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 340, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvUk9CRVJULlJPUVVF"}, {"spi:groupname": "WCMO-SUP-LEAD", "_rowstamp": "3890849", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/4-425", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/4-425/maxgroup/0-35", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "2215874214", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 35, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 425, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1VQLUxFQUQvUk9CRVJULlJPUVVF"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "3888177", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/5-424", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/5-424/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 424, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9ST0JFUlQuUk9RVUU-"}, {"spi:groupname": "HR-SPECIALIST", "_rowstamp": "5446086", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/6-438", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9CRVJULlJPUVVF/user/0-46/groupuser/6-438/maxgroup/0-98", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494801", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 98, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "HR Specialists", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0hSLVNQRUNJQUxJU1Q-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 438, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0hSLVNQRUNJQUxJU1QvUk9CRVJULlJPUVVF"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Public Works Manager", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "ROBERT.ROQUE", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:29:17+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 49, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON><PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2377636524", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:city": "ARIFJAN", "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-7758-0817", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Teague", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--", "spi:displayname": "<PERSON><PERSON>, <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "REMOVED", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "RONALD.TEAGUE", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 49, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUk9OQUxELlRFQUdVRQ--", "spi:sysuser": false, "spi:defstoreroom": "LCVK-CMW-CAS", "spi:defsite": "LCVKWT", "_rowstamp": "*********4", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3391799", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/0-105", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/0-105/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 105, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1JPTkFMRC5URUFHVUU-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3391800", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/1-104", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/1-104/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 104, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1JPTkFMRC5URUFHVUU-"}, {"spi:groupname": "WCMO-SUP-LEAD", "_rowstamp": "3408083", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/2-296", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/2-296/maxgroup/0-35", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "2215874214", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 35, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 296, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1VQLUxFQUQvUk9OQUxELlRFQUdVRQ--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411652", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/3-342", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/3-342/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 342, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvUk9OQUxELlRFQUdVRQ--"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "229486765", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/4-18235", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_Uk9OQUxELlRFQUdVRQ--/user/0-49/groupuser/4-18235/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 18235, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL1JPTkFMRC5URUFHVUU-"}], "spi:screenreader": false, "spi:status_description": "Removed due to no usage", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Work Center Shift Supervisor", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "KW", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:personid": "RONALD.TEAGUE", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:32:05+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 51, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2377605940", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--", "spi:displayname": "<PERSON>, <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "NSGBA", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "LANCE.BOYD", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 50, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvTEFOQ0UuQk9ZRA--", "spi:sysuser": false, "spi:defsite": "NSGBA", "_rowstamp": "1481755668", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3391925", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/0-107", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/0-107/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 107, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0xBTkNFLkJPWUQ-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3391926", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/1-106", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/1-106/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 106, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0xBTkNFLkJPWUQ-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411654", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/2-343", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/2-343/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 343, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvTEFOQ0UuQk9ZRA--"}, {"spi:groupname": "NSGBAUSERS", "_rowstamp": "77577779", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/3-5748", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/3-5748/maxgroup/0-117", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018775", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 117, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "NSGBA Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL05TR0JBVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 5748, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL05TR0JBVVNFUlMvTEFOQ0UuQk9ZRA--"}, {"spi:groupname": "PMO-MGR", "_rowstamp": "132666754", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/4-13479", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/4-13479/maxgroup/0-38", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495003", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 38, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PMO Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BNTy1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 13479, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BNTy1NR1IvTEFOQ0UuQk9ZRA--"}, {"spi:groupname": "WCMO-SUP-LEAD", "_rowstamp": "1037994127", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/5-1710675", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/5-1710675/maxgroup/0-35", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "2215874214", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 35, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "WCMO Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dDTU8tU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 1710675, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dDTU8tU1VQLUxFQUQvTEFOQ0UuQk9ZRA--"}, {"spi:groupname": "WHSE-SUP-LEAD", "_rowstamp": "1961880653", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/6-2371091", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_TEFOQ0UuQk9ZRA--/user/0-50/groupuser/6-2371091/maxgroup/0-56", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495490", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 56, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Warehouse  Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0UtU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 2371091, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0UtU1VQLUxFQUQvTEFOQ0UuQk9ZRA--"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "US/Eastern", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USNAVY", "spi:timezone_description": "US/Eastern (UTC-5)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "NSGBA", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "LANCE.BOYD", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:33:39+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 52, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "CAS-1025", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2372331644", "spi:firstname": "<PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:city": "Doha", "spi:employeetype_description": "Company Employee", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--", "spi:displayname": "Rumpon, Haren - Vectrus - NoExport", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "REMOVED", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "RUMP125314", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 51, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUlVNUDEyNTMxNA--", "spi:sysuser": false, "spi:defstoreroom": "LCVK-CMW-CAS", "spi:defsite": "LCVKWT", "_rowstamp": "*********6", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3392052", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/0-109", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/0-109/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 109, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1JVTVAxMjUzMTQ-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3392053", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/1-108", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/1-108/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 108, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1JVTVAxMjUzMTQ-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3406238", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/2-240", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/2-240/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 240, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvUlVNUDEyNTMxNA--"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "229486763", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/3-18237", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/3-18237/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 18237, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL1JVTVAxMjUzMTQ-"}, {"spi:groupname": "QUA-INSPECTOR", "_rowstamp": "509207129", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/4-461366", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UlVNUDEyNTMxNA--/user/0-51/groupuser/4-461366/maxgroup/0-78", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495240", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 78, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Quality Specialist and Inspectors", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1FVQS1JTlNQRUNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 461366, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1FVQS1JTlNQRUNUT1IvUlVNUDEyNTMxNA--"}], "spi:screenreader": false, "spi:status_description": "Removed due to no usage", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Quality Auditor", "spi:stateprovince": "NA", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:country": "Qatar", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:addressline1": "NA", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:department": "OSO", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:postalcode": "NA", "spi:personid": "RUMP125314", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:33:56+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 53, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "12087472", "spi:firstname": "Francisco", "spi:plusgtechauthority": false, "spi:primaryphone": "+974-3007-1967", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--", "spi:displayname": "<PERSON>, <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "FRANCISCO.HERNANDEZ", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "Francisco<PERSON>@vectrus.com", "spi:maxuserid": 52, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvRlJBTkNJU0NPLkhFUk5BTkRFWg--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "*********7", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3392071", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52/groupuser/0-111", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52/groupuser/0-111/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 111, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0ZSQU5DSVNDTy5IRVJOQU5ERVo-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3392072", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52/groupuser/1-110", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52/groupuser/1-110/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 110, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0ZSQU5DSVNDTy5IRVJOQU5ERVo-"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "3407516", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52/groupuser/2-268", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52/groupuser/2-268/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 268, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9GUkFOQ0lTQ08uSEVSTkFOREVa"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411656", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52/groupuser/3-344", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_RlJBTkNJU0NPLkhFUk5BTkRFWg--/user/0-52/groupuser/3-344/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 344, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvRlJBTkNJU0NPLkhFUk5BTkRFWg--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "FRANCISCO.HERNANDEZ", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:34:55+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 54, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "Francisco<PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "DEVIN.BUTLER", "spi:plusggastestauth": false, "_rowstamp": "2363817733", "spi:firstname": "Virginia", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-3003-8417", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "Gutzy", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-", "spi:displayname": "Gutzy, Virginia - Vectrus - No Export", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "VIRGINIA.GUTZY", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 53, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvVklSR0lOSUEuR1VUWlk-", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "*********8", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3392318", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53/groupuser/0-113", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53/groupuser/0-113/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 113, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1ZJUkdJTklBLkdVVFpZ"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3392319", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53/groupuser/1-112", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53/groupuser/1-112/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 112, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1ZJUkdJTklBLkdVVFpZ"}, {"spi:groupname": "MWR-MGR", "_rowstamp": "3406812", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53/groupuser/2-251", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53/groupuser/2-251/maxgroup/0-59", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494960", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 59, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "MWR Managers", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01XUi1NR1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 251, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01XUi1NR1IvVklSR0lOSUEuR1VUWlk-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411658", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53/groupuser/3-345", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_VklSR0lOSUEuR1VUWlk-/user/0-53/groupuser/3-345/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 345, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvVklSR0lOSUEuR1VUWlk-"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "MWR Manager", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "EXTSYS1", "spi:plusghfapprover": false, "spi:personid": "VIRGINIA.GUTZY", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:37:19+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 55, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "RONALD.TEAGUE", "spi:plusggastestauth": false, "_rowstamp": "2372331645", "spi:firstname": "<PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:primaryphone": "+974-3155-3539", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--", "spi:displayname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> No Export", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "GOPA124848", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<PERSON><PERSON><PERSON>@vectrus.com", "spi:maxuserid": 55, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR09QQTEyNDg0OA--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "*********9", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3392466", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55/groupuser/0-115", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55/groupuser/0-115/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 115, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0dPUEExMjQ4NDg-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3392467", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55/groupuser/1-114", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55/groupuser/1-114/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 114, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0dPUEExMjQ4NDg-"}, {"spi:groupname": "IT-TECHNICIAN", "_rowstamp": "3406363", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55/groupuser/2-243", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55/groupuser/2-243/maxgroup/0-44", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419235", "spi:authallgls": false, "spi:sctemplateid": 6, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 44, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "IT Technicians", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0lULVRFQ0hOSUNJQU4-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 243, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0lULVRFQ0hOSUNJQU4vR09QQTEyNDg0OA--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411660", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55/groupuser/3-346", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_R09QQTEyNDg0OA--/user/0-55/groupuser/3-346/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 346, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvR09QQTEyNDg0OA--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:department": "PWB", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "GOPA124848", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:38:42+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 56, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON><PERSON><PERSON>@gov2x.com"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "12087632", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--", "spi:displayname": "<PERSON>, <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "BAKE124095", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 54, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvQkFLRTEyNDA5NQ--", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280530", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3392479", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54/groupuser/0-117", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54/groupuser/0-117/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 117, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL0JBS0UxMjQwOTU-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3392480", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54/groupuser/1-116", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54/groupuser/1-116/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 116, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL0JBS0UxMjQwOTU-"}, {"spi:groupname": "PWD-SUP-LEAD", "_rowstamp": "3407535", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54/groupuser/2-287", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54/groupuser/2-287/maxgroup/0-42", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419953", "spi:authallgls": false, "spi:sctemplateid": 8, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 42, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1TVVAtTEVBRA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 287, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1TVVAtTEVBRC9CQUtFMTI0MDk1"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3413791", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54/groupuser/3-394", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_QkFLRTEyNDA5NQ--/user/0-54/groupuser/3-394/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 394, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvQkFLRTEyNDA5NQ--"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "BAKE124095", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:38:56+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 57, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "ERBIL", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:supervisor": "CALH387786", "spi:caltype": "gregorian", "spi:plusggastestauth": false, "_rowstamp": "2363817734", "spi:firstname": "<PERSON><PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:shiptoaddress": "11084", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON><PERSON><PERSON>", "spi:language": "EN", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--", "spi:displayname": "Sa<PERSON>hevan2, <PERSON><PERSON><PERSON> - <PERSON><PERSON>trus No Export", "spi:plusgisolationauth": false, "spi:billtoaddress": "VECTRUS", "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "ACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56", "spi:cognossyncstatus": "notenabled", "spi:storeroomsite": "LCVIRQ", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "SAHA124824", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<PERSON><PERSON><PERSON>.<EMAIL>", "spi:maxuserid": 56, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvU0FIQTEyNDgyNA--", "spi:sysuser": false, "spi:defstoreroom": "CMW-ERB", "spi:defsite": "LCVIRQ", "_rowstamp": "2419777272", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3392621", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/0-119", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/0-119/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 119, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1NBSEExMjQ4MjQ-"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3392622", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/1-118", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/1-118/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 118, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1NBSEExMjQ4MjQ-"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411662", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/2-347", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/2-347/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 347, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvU0FIQTEyNDgyNA--"}, {"spi:groupname": "LCVIRQUSERS", "_rowstamp": "610568992", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/3-714089", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/3-714089/maxgroup/0-110", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018756", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 110, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "Iraq Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVklSUVVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 714089, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVklSUVVTRVJTL1NBSEExMjQ4MjQ-"}, {"spi:groupname": "PWD-TECHNICIAN", "_rowstamp": "610705828", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/4-714236", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0FIQTEyNDgyNA--/user/0-56/groupuser/4-714236/maxgroup/0-43", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3419253", "spi:authallgls": false, "spi:sctemplateid": 6, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 43, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "PWD Technicians", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BXRC1URUNITklDSUFO", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 714236, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BXRC1URUNITklDSUFOL1NBSEExMjQ4MjQ-"}], "spi:screenreader": false, "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Maintenance Mechanic", "spi:locale_description": "English", "spi:status_description": "Active", "spi:timezone": "Asia/Baghdad", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Baghdad (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVIRQ", "spi:deviceclass_description": "Advanced", "spi:department": "Field Maintenance", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "SAHA124824", "spi:wfmailelection": "PROCESS", "spi:caltype_description": "Gregorian Calendar", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:40:03+00:00", "spi:transemailelection_description": "Never Notify", "spi:locale": "en", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:droppoint": "Field Maintenance", "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 58, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<PERSON><PERSON><PERSON>.<EMAIL>"}, {"spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "12087460", "spi:firstname": "<PERSON><PERSON><PERSON>", "spi:plusgtechauthority": false, "spi:primaryphone": "+974-5538-9718", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-", "spi:displayname": "<PERSON><PERSON><PERSON>, Shamboo - Vectrus - No Export", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:querywithsite": true, "spi:status": "INACTIVE", "spi:sidenav": 0, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58", "spi:cognossyncstatus": "notenabled", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "SHAMBOO.SHARAN", "spi:forceexpiration": false, "spi:password": "", "spi:failedlogins": 0, "spi:loginid": "<EMAIL>", "spi:maxuserid": 58, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvU0hBTUJPTy5TSEFSQU4-", "spi:sysuser": false, "spi:defsite": "QBOSS", "_rowstamp": "1449280533", "spi:groupuser": [{"spi:groupname": "MAXEVERYONE", "_rowstamp": "3392856", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58/groupuser/0-123", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58/groupuser/0-123/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 123, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1NIQU1CT08uU0hBUkFO"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3392857", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58/groupuser/1-122", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58/groupuser/1-122/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 122, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1NIQU1CT08uU0hBUkFO"}, {"spi:groupname": "WHSE-Specialist", "_rowstamp": "3408560", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58/groupuser/2-310", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58/groupuser/2-310/maxgroup/0-57", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "193353218", "spi:authallgls": false, "spi:sctemplateid": 39, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 57, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Warehouse Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0UtU3BlY2lhbGlzdA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 310, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0UtU3BlY2lhbGlzdC9TSEFNQk9PLlNIQVJBTg--"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3411664", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58/groupuser/3-348", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_U0hBTUJPTy5TSEFSQU4-/user/0-58/groupuser/3-348/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 348, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvU0hBTUJPTy5TSEFSQU4-"}], "spi:screenreader": false, "spi:status_description": "Inactive", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:status": "ACTIVE", "spi:country": "QA", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "QBOSS", "spi:deviceclass_description": "Advanced", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:plusghfapprover": false, "spi:personid": "SHAMBOO.SHARAN", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:42:27+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 60, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}, {"spi:location": "CAS-3284", "spi:wfmailelection_description": "Notify based on the process", "spi:plusgptwrevapp": false, "spi:plusgsolnapprover": false, "spi:plusggastestauth": false, "_rowstamp": "2363817735", "spi:firstname": "<PERSON>", "spi:plusgtechauthority": false, "spi:employeetype_description": "Company Employee", "spi:hiredate": "2021-10-16T00:00:00+00:00", "spi:plusgauditapprover": false, "spi:plusgptwissuecounter": 0, "spi:lastname": "<PERSON><PERSON><PERSON>", "spi:loctoservreq": true, "spi:plusgbyprevapp": false, "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-", "spi:displayname": "Pepito, Nelson - V2X - MMD", "spi:plusgisolationauth": false, "spi:maxuser": [{"spi:status": "ACTIVE", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60", "spi:cognossyncstatus": "notenabled", "spi:forceexpiration": false, "spi:failedlogins": 0, "spi:defstoreroom": "LCVK-CMW-CAS", "_rowstamp": "2366584328", "spi:groupuser": [{"spi:groupname": "PROP-SPECIALIST", "_rowstamp": "7061117", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/0-456", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/0-456/maxgroup/0-68", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "12213675", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 68, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Property Specialist", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1BST1AtU1BFQ0lBTElTVA--", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 456, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1BST1AtU1BFQ0lBTElTVC9QRVBJNDM5Mw--"}, {"spi:groupname": "MAXEVERYONE", "_rowstamp": "3392966", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/1-125", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/1-125/maxgroup/0-7", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "All Maximo Users", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWEVWRVJZT05F", "spi:pluspauthperslst": false, "_rowstamp": "789060", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 7, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 125, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEVWRVJZT05FL1BFUEk0Mzkz"}, {"spi:groupname": "MAXDEFLTREG", "_rowstamp": "3392967", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/2-124", "spi:maxgroup": [{"spi:authlaborself": false, "spi:nullrepfac": true, "spi:sidenav": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/2-124/maxgroup/0-6", "spi:pluspauthallcust": false, "spi:authpersongroup": false, "spi:description": "New User", "spi:authlaborcrew": false, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL01BWERFRkxUUkVH", "spi:pluspauthperslst": false, "_rowstamp": "789059", "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:authallgls": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 6, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false}], "spi:groupuserid": 124, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWERFRkxUUkVHL1BFUEk0Mzkz"}, {"spi:groupname": "DTR-REQUESTOR", "_rowstamp": "3413790", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/3-393", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/3-393/maxgroup/0-70", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3494645", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 70, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Desktop Requisition Requestor", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0RUUi1SRVFVRVNUT1I-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 393, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0RUUi1SRVFVRVNUT1IvUEVQSTQzOTM-"}, {"spi:groupname": "WHSE-SUP-LEAD", "_rowstamp": "93867716", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/4-6772", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/4-6772/maxgroup/0-56", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 90, "spi:pluspauthperslst": false, "_rowstamp": "3495490", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 56, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Warehouse  Supervisor and Leads", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dIU0UtU1VQLUxFQUQ-", "spi:passwordwarning": 5, "spi:authallstorerooms": true, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 6772, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dIU0UtU1VQLUxFQUQvUEVQSTQzOTM-"}, {"spi:groupname": "LCVKWTUSERS", "_rowstamp": "229486775", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/5-18225", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/5-18225/maxgroup/0-136", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018763", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 136, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": false, "spi:authpersongroup": false, "spi:description": "LCV KUWAIT Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL0xDVktXVFVTRVJT", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 18225, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL0xDVktXVFVTRVJTL1BFUEk0Mzkz"}, {"spi:groupname": "QBOSSUSERS", "_rowstamp": "244520842", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/6-19611", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/6-19611/maxgroup/0-103", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "133018780", "spi:authallgls": false, "spi:sctemplateid": 23, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 103, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "QBOSS Users", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1FCT1NTVVNFUlM-", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": false, "spi:authlaborsuper": false}], "spi:groupuserid": 19611, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1FCT1NTVVNFUlMvUEVQSTQzOTM-"}, {"spi:groupname": "WO_READ_RUNREPORTS", "_rowstamp": "609855868", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/7-711891", "spi:maxgroup": [{"spi:nullrepfac": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser/_UEVQSTQzOTM-/user/0-60/groupuser/7-711891/maxgroup/0-128", "spi:pluspauthallcust": false, "spi:authlaborcrew": false, "spi:passwordduration": 365, "spi:pluspauthperslst": false, "_rowstamp": "1146222668", "spi:authallgls": false, "spi:sctemplateid": 11, "spi:pluspauthcustvnd": false, "spi:authallsites": false, "spi:maxgroupid": 128, "spi:pluspauthnoncust": false, "spi:authallrepfacs": false, "spi:authlaborself": false, "spi:sidenav": true, "spi:authpersongroup": false, "spi:description": "Work Tracking read only and run reports", "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL01BWEdST1VQL1dPX1JFQURfUlVOUkVQT1JUUw--", "spi:passwordwarning": 5, "spi:authallstorerooms": false, "spi:pluspauthgrplst": false, "spi:independent": false, "spi:authlaborall": true, "spi:authlaborsuper": false}], "spi:groupuserid": 711891, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvR1JPVVBVU0VSL1dPX1JFQURfUlVOUkVQT1JUUy9QRVBJNDM5Mw--"}], "spi:masissuer": "saml", "spi:screenreader": false, "spi:querywithsite": true, "spi:sidenav": 0, "spi:storeroomsite": "LCVKWT", "spi:isconsultant": false, "spi:inactivesites": false, "spi:userid": "PEPI4393", "spi:password": "", "spi:loginid": "<EMAIL>", "spi:maxuserid": 60, "rdf:about": "http://childkey#UEVSU09OL01BWFVTRVIvUEVQSTQzOTM-", "spi:sysuser": false, "spi:defsite": "LCVKWT", "spi:status_description": "Active", "spi:type_description": "Project Site Users", "spi:type": "TYPE 1"}], "spi:plusgsitecheckerauth": false, "spi:title": "Materiel Supervisor", "spi:status_description": "Active", "spi:timezone": "Asia/Kuwait", "spi:deviceclass": 2, "spi:employeetype": "EMPLOYEE", "spi:status": "ACTIVE", "spi:locationorg": "USARMY", "spi:timezone_description": "Asia/Kuwait (UTC+3)", "spi:transemailelection": "NEVER", "spi:plusgmocrevapp": false, "spi:locationsite": "LCVKWT", "spi:deviceclass_description": "Advanced", "spi:department": "1600 - Materiel Management (MM", "spi:languserupdated": false, "spi:plusgfinauthority": false, "spi:sendersysid": "MASSYNC", "spi:plusghfapprover": false, "spi:personid": "PEPI4393", "spi:wfmailelection": "PROCESS", "spi:plusgperissueauth": false, "spi:plusgradiosrcauth": false, "spi:pluspcustvndtype": "INTERNAL", "spi:plusgperperfauth": false, "spi:statusdate": "2020-05-25T13:43:24+00:00", "spi:transemailelection_description": "Never Notify", "spi:plusgauthlockmgmt": false, "spi:plusgrcfaapprover": false, "spi:plusgriskrevapp": false, "spi:plusgisospec": false, "spi:plusgcertrevapp": false, "spi:personuid": 61, "spi:plusgperareaauth": false, "spi:plusgiscertified": false, "spi:pluspcustvndtype_description": "Internal", "spi:acceptingwfmail": true, "spi:plusgconfspaceauth": false, "spi:primaryemail": "<EMAIL>"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiperuser"}