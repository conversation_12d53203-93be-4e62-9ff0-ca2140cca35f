{"oslc:responseInfo": {"totalPages": 1, "oslc:totalCount": 5, "pagenum": 1, "rdf:about": "http://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization?lean=0&oslc.pageSize=50&oslc.select=%2A"}, "rdfs:member": [{"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_T01EQUM-/site/0-34", "spi:siteuid": 34, "spi:description": "OMDAC Program", "spi:changeby": "KRISH113979", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvT01EQUNQUkc-", "spi:enterdate": "2025-01-10T14:21:41+00:00", "spi:enterby": "KRISH113979", "_rowstamp": "2193387237", "spi:active": true, "spi:contact": "HAMP116428", "spi:changedate": "2025-01-10T14:21:55+00:00", "spi:plusgopenomuid": 31, "spi:systemid": "OMDACLOC", "spi:siteid": "OMDACPRG"}], "spi:itemsetid": "ITEMSET4", "spi:description": "OMDAC Programs", "spi:dfltitemstatus_description": "Pending", "spi:dfltitemstatus": "PENDING", "spi:orgid": "OMDAC", "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_T01EQUM-", "spi:enterdate": "2025-01-10T14:17:32+00:00", "spi:enterby": "KRISH113979", "spi:plusgaddassetspec": true, "_rowstamp": "2193386143", "spi:category": "STK", "spi:clearingacct": "0000.0000.0000", "spi:companysetid": "COMPSET4", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 12, "spi:basecurrency1": "USD"}, {"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16", "spi:siteuid": 16, "spi:description": "Thule Air Base", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvVEhVTEU-", "spi:enterdate": "2021-09-27T02:12:17+00:00", "spi:enterby": "DATASPLICE", "_rowstamp": "*********", "spi:active": true, "spi:contact": "WILLIAM.WOMACK", "spi:changedate": "2022-11-07T10:15:45+00:00", "spi:plusgopenomuid": 14, "spi:systemid": "THULELOC", "spi:siteid": "THULE", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "*********", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/0-31", "spi:addresscode": "30001", "spi:billtoshiptoid": 31, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDAxL1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "408469260", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/1-29", "spi:addresscode": "30003", "spi:billtoshiptoid": 29, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDAzL1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583525", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/2-30", "spi:addresscode": "30004", "spi:billtoshiptoid": 30, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA0L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583527", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/3-32", "spi:addresscode": "30005", "spi:billtoshiptoid": 32, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA1L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583528", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/4-33", "spi:addresscode": "30006", "spi:billtoshiptoid": 33, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA2L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583529", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/5-34", "spi:addresscode": "30007", "spi:billtoshiptoid": 34, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA3L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": true, "_rowstamp": "115583808", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/6-35", "spi:addresscode": "30008", "spi:billtoshiptoid": 35, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA4L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "408470027", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/7-54", "spi:addresscode": "30009", "spi:billtoshiptoid": 54, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA5L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583809", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/8-27", "spi:addresscode": "MCGUIRE AFB - US", "spi:billtoshiptoid": 27, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL01DR1VJUkUgQUZCIC0gVVMvVVNBRi9USFVMRQ--"}, {"spi:shiptodefault": false, "_rowstamp": "408470028", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/9-28", "spi:addresscode": "<EMAIL>", "spi:billtoshiptoid": 28, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVNJTlZPSUNFU0BWRUNUUlVTLkNPTS9VU0FGL1RIVUxF"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/1-24", "spi:siteuid": 24, "spi:description": "<PERSON>ppard Air Force Base", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvU0hFUEQ-", "spi:enterdate": "2022-01-20T16:48:35+00:00", "spi:enterby": "JAIN383011", "_rowstamp": "474861216", "spi:active": true, "spi:changedate": "2022-11-07T10:15:49+00:00", "spi:plusgopenomuid": 22, "spi:systemid": "SHEPDLOC", "spi:siteid": "SHEPD", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "190260395", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/1-24/billtoshipto/0-37", "spi:addresscode": "11072", "spi:billtoshiptoid": 37, "spi:billto": true, "spi:shipto": true, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDcyL1VTQUYvU0hFUEQ-"}]}], "spi:itemsetid": "ITEMSET2", "spi:description": "United States Air Force", "spi:dfltitemstatus_description": "Active", "spi:dfltitemstatus": "ACTIVE", "spi:orgid": "USAF", "spi:address": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/0-33", "spi:addressid": 33, "spi:description": "Sheppard AFB", "spi:changeby": "JAIN383011", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNzIvVVNBRg--", "_rowstamp": "190260224", "spi:addresscode": "11072", "spi:address5": "US", "spi:address4": "76311", "spi:address3": "TX", "spi:changedate": "2022-01-20T16:50:34+00:00", "spi:address2": "Wichita Falls", "spi:address1": "235 9th Avenue Bldg. 1404"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/1-20", "spi:addressid": 20, "spi:description": "Vectrus HQ", "spi:changeby": "KRISH113979", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDEvVVNBRg--", "_rowstamp": "115580885", "spi:addresscode": "30001", "spi:address5": "US", "spi:address4": "80919", "spi:address3": "CO", "spi:changedate": "2021-09-27T03:11:36+00:00", "spi:address2": "Colorado Springs", "spi:address1": "2424 Garden of the Gods Road, Suite 300"}, {"_rowstamp": "290240184", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/2-21", "spi:addressid": 21, "spi:description": "Vectrus Services A/S", "spi:addresscode": "30003", "spi:address5": "DK", "spi:address4": "2100", "spi:changeby": "PALA383021", "spi:changedate": "2022-05-10T04:17:05+00:00", "spi:address2": "Copenhagen", "spi:address1": "Vectrus Services, Indiavej 1,4", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDMvVVNBRg--"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/3-22", "spi:addressid": 22, "spi:description": "NORFOLK", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDQvVVNBRg--", "_rowstamp": "290240707", "spi:addresscode": "30004", "spi:address5": "US", "spi:address4": "23511", "spi:address3": "VA", "spi:changedate": "2022-05-10T04:18:01+00:00", "spi:address2": "NORFOLK", "spi:address1": "Pacer Goose, Intermodal Hub, 9551 Decatur Ave"}, {"_rowstamp": "115581388", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/4-23", "spi:addressid": 23, "spi:description": "Schenker A/S", "spi:addresscode": "30005", "spi:address5": "GL", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:14:32+00:00", "spi:address1": "<PERSON><PERSON><PERSON><PERSON>en 31-39, - Car<PERSON> 1, <PERSON> 58-60", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDUvVVNBRg--"}, {"_rowstamp": "115581750", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/5-24", "spi:addressid": 24, "spi:description": "FREDERCIA", "spi:addresscode": "30006", "spi:address5": "DK", "spi:address4": "7000", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:16:01+00:00", "spi:address2": "Fredericia", "spi:address1": "Greenland Warehouse c/oShipping.dk,Oceankajen 12", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDYvVVNBRg--"}, {"_rowstamp": "286515390", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/6-25", "spi:addressid": 25, "spi:description": "KASTRUP", "spi:addresscode": "30007", "spi:address5": "DK", "spi:address4": "2770", "spi:changeby": "PALA383021", "spi:changedate": "2022-05-05T13:28:21+00:00", "spi:address2": "Kastrup", "spi:address1": "Vectrus Services A/S, Worldwide Flight Services", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDcvVVNBRg--"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/7-26", "spi:addressid": 26, "spi:description": "MCGUIRE", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDgvVVNBRg--", "_rowstamp": "207611220", "spi:addresscode": "30008", "spi:address5": "US", "spi:address4": "08641", "spi:address3": "NJ", "spi:changedate": "2022-02-10T03:24:17+00:00", "spi:address2": "Joint Base MDL", "spi:address1": "Vectrus Services A/S 1757 Vandenberg Avenue"}, {"_rowstamp": "951841678", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/8-45", "spi:addressid": 45, "spi:description": "Vectrus Services A/S", "spi:addresscode": "30009", "spi:address5": "Denmark", "spi:changeby": "MOOR382170", "spi:address3": "<EMAIL>", "spi:changedate": "2023-10-30T18:00:54+00:00", "spi:address2": "2900 <PERSON><PERSON><PERSON>", "spi:address1": "Strandvejen 125", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDkvVVNBRg--"}, {"_rowstamp": "115582653", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/9-27", "spi:addressid": 27, "spi:addresscode": "AIRSIDE ASSIST - DENMARK", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:19:36+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvQUlSU0lERSBBU1NJU1QgLSBERU5NQVJLL1VTQUY-"}, {"_rowstamp": "115582654", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/10-28", "spi:addressid": 28, "spi:addresscode": "COPENHAGEN OFFICE", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:19:47+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvQ09QRU5IQUdFTiBPRkZJQ0UvVVNBRg--"}, {"_rowstamp": "115582655", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/11-29", "spi:addressid": 29, "spi:addresscode": "MCGUIRE AFB - US", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:19:58+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvTUNHVUlSRSBBRkIgLSBVUy9VU0FG"}, {"_rowstamp": "115582656", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/12-30", "spi:addressid": 30, "spi:addresscode": "NORFOLK - US", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:20:06+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvTk9SRk9MSyAtIFVTL1VTQUY-"}, {"_rowstamp": "115582657", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/13-31", "spi:addressid": 31, "spi:addresscode": "PORT FEDERICA- DENMARK", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:20:16+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvUE9SVCBGRURFUklDQS0gREVOTUFSSy9VU0FG"}, {"_rowstamp": "115582658", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/14-32", "spi:addressid": 32, "spi:description": "Vectrus Services A/S", "spi:addresscode": "<EMAIL>", "spi:address5": "Denmark", "spi:changeby": "KRISH113979", "spi:address3": "København", "spi:changedate": "2021-09-27T03:20:51+00:00", "spi:address2": "2100 København Ø", "spi:address1": "Indiavej 1,3", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvVkVDVFJVU0lOVk9JQ0VTQFZFQ1RSVVMuQ09NL1VTQUY-"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--", "spi:enterdate": "2021-09-27T02:05:08+00:00", "spi:enterby": "DATASPLICE", "spi:plusgaddassetspec": true, "_rowstamp": "*********", "spi:category": "STK", "spi:clearingacct": "0000.0000.00000", "spi:companysetid": "COMPSET2", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 8, "spi:basecurrency1": "USD"}, {"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/0-1", "spi:siteuid": 1, "spi:description": "Qatar Base Operations Support & Service Bridge.", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvUUJPU1M-", "spi:enterdate": "2020-05-22T12:53:52+00:00", "spi:enterby": "PRABHAK", "_rowstamp": "*********", "spi:active": true, "spi:contact": "SOTO111313", "spi:changedate": "2022-11-07T10:13:39+00:00", "spi:plusgopenomuid": 1, "spi:systemid": "QATARLOC", "spi:siteid": "QBOSS", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "28530854", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/0-1/billtoshipto/0-1", "spi:shiptocontact": "SOTO111313", "spi:addresscode": "QBOSS", "spi:billtocontact": "SOTO111313", "spi:billtoshiptoid": 1, "spi:billto": true, "spi:shipto": true, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1FCT1NTL1VTQVJNWS9RQk9TUw--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10", "spi:siteuid": 10, "spi:description": "Logcap Thailand", "spi:changeby": "KUBE920000027", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTENWVA--", "spi:enterdate": "2021-01-21T09:10:02+00:00", "spi:enterby": "SHIV125743", "_rowstamp": "474859771", "spi:active": true, "spi:contact": "NORAMINA.ALONTO", "spi:changedate": "2022-11-07T10:13:42+00:00", "spi:plusgopenomuid": 10, "spi:systemid": "THAILANDLOC", "spi:siteid": "LCVT", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "470137149", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/0-55", "spi:shiptocontact": "MICHAEL.FIGUEROA", "spi:addresscode": "11095", "spi:billtoshiptoid": 55, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk1L1VTQVJNWS9MQ1ZU"}, {"spi:shiptodefault": true, "_rowstamp": "470137150", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/1-56", "spi:shiptocontact": "MICHAEL.FIGUEROA", "spi:addresscode": "24001", "spi:billtoshiptoid": 56, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzI0MDAxL1VTQVJNWS9MQ1ZU"}, {"spi:shiptodefault": false, "_rowstamp": "470137151", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/2-57", "spi:shiptocontact": "MICHAEL.FIGUEROA", "spi:addresscode": "24002", "spi:billtoshiptoid": 57, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzI0MDAyL1VTQVJNWS9MQ1ZU"}, {"spi:shiptodefault": false, "_rowstamp": "470137152", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/3-58", "spi:shiptocontact": "MICHAEL.FIGUEROA", "spi:addresscode": "24099", "spi:billtoshiptoid": 58, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzI0MDk5L1VTQVJNWS9MQ1ZU"}, {"spi:shiptodefault": false, "_rowstamp": "470136977", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/4-16", "spi:addresscode": "LCVT", "spi:billtoshiptoid": 16, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL0xDVlQvVVNBUk1ZL0xDVlQ-"}, {"spi:shiptodefault": false, "_rowstamp": "532352894", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/5-64", "spi:shiptocontact": "NORAMINA.ALONTO", "spi:addresscode": "LCVT APO", "spi:billtoshiptoid": 64, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL0xDVlQgQVBPL1VTQVJNWS9MQ1ZU"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/2-11", "spi:siteuid": 11, "spi:description": "Logcap Iraq", "spi:changeby": "KUBE920000027", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTENWSVJR", "spi:enterdate": "2021-04-15T14:36:47+00:00", "spi:enterby": "SHIV125743", "_rowstamp": "474859772", "spi:active": true, "spi:contact": "HALL369408", "spi:changedate": "2022-11-07T10:13:44+00:00", "spi:plusgopenomuid": 11, "spi:systemid": "IRAQLOC", "spi:siteid": "LCVIRQ", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "256556390", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/2-11/billtoshipto/0-42", "spi:addresscode": "11039", "spi:billtoshiptoid": 42, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDM5L1VTQVJNWS9MQ1ZJUlE-"}, {"spi:shiptodefault": false, "_rowstamp": "260162722", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/2-11/billtoshipto/1-44", "spi:addresscode": "11084", "spi:billtoshiptoid": 44, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDg0L1VTQVJNWS9MQ1ZJUlE-"}, {"spi:shiptodefault": false, "_rowstamp": "256557279", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/2-11/billtoshipto/2-43", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 43, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNBUk1ZL0xDVklSUQ--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14", "spi:siteuid": 14, "spi:description": "Logcap Kuwait", "spi:changeby": "KUBE920000027", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTENWS1dU", "spi:enterdate": "2021-09-08T16:45:01+00:00", "spi:enterby": "KANJ125715", "_rowstamp": "474859773", "spi:active": true, "spi:contact": "SOFG118757", "spi:changedate": "2022-11-07T10:13:47+00:00", "spi:plusgopenomuid": 13, "spi:systemid": "KUWAITLOC", "spi:siteid": "LCVKWT", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "98392800", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/0-24", "spi:addresscode": "11013", "spi:billtoshiptoid": 24, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEzL1VTQVJNWS9MQ1ZLV1Q-"}, {"spi:shiptodefault": false, "_rowstamp": "98392801", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/1-25", "spi:addresscode": "11054", "spi:billtoshiptoid": 25, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDU0L1VTQVJNWS9MQ1ZLV1Q-"}, {"spi:shiptodefault": true, "_rowstamp": "98392802", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/2-26", "spi:addresscode": "11073", "spi:billtoshiptoid": 26, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDczL1VTQVJNWS9MQ1ZLV1Q-"}, {"spi:shiptodefault": false, "_rowstamp": "1525703436", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/3-41", "spi:shiptocontact": "DAVI369173", "spi:addresscode": "11098", "spi:billtoshiptoid": 41, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk4L1VTQVJNWS9MQ1ZLV1Q-"}, {"spi:shiptodefault": false, "_rowstamp": "98392799", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/4-23", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 23, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNBUk1ZL0xDVktXVA--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/4-25", "spi:siteuid": 25, "spi:description": "Kuwait DFAC", "spi:changeby": "KRISH113979", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvS0RGQUM-", "spi:enterdate": "2022-02-11T06:08:41+00:00", "spi:enterby": "PALA383021", "_rowstamp": "765283371", "spi:active": true, "spi:changedate": "2023-06-29T16:01:25+00:00", "spi:plusgopenomuid": 23, "spi:systemid": "KDFACKUWAIT", "spi:siteid": "KDFAC", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "208610704", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/4-25/billtoshipto/0-38", "spi:addresscode": "11073", "spi:billtoshiptoid": 38, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDczL1VTQVJNWS9LREZBQw--"}, {"spi:shiptodefault": false, "_rowstamp": "208610705", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/4-25/billtoshipto/1-39", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 39, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNBUk1ZL0tERkFD"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/5-26", "spi:siteuid": 26, "spi:description": "Kwajalein Range", "spi:changeby": "KUBE920000027", "spi:vecfreight": "15", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvSUtXQUo-", "spi:enterdate": "2022-05-07T02:33:47+00:00", "spi:enterby": "KRISH113979", "_rowstamp": "474859775", "spi:active": true, "spi:contact": "BILL.COOLER", "spi:changedate": "2022-11-07T10:14:03+00:00", "spi:plusgopenomuid": 24, "spi:systemid": "KWAJLOC", "spi:siteid": "IKWAJ", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "289616590", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/5-26/billtoshipto/0-50", "spi:addresscode": "110100", "spi:billtoshiptoid": 50, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEwMC9VU0FSTVkvSUtXQUo-"}, {"spi:shiptodefault": true, "_rowstamp": "1145752602", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/5-26/billtoshipto/1-45", "spi:shiptocontact": "DONA384628", "spi:addresscode": "11089", "spi:billtoshiptoid": 45, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDg5L1VTQVJNWS9JS1dBSg--"}, {"spi:shiptodefault": false, "_rowstamp": "289616253", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/5-26/billtoshipto/2-49", "spi:addresscode": "11099", "spi:billtoshiptoid": 49, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk5L1VTQVJNWS9JS1dBSg--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28", "spi:siteuid": 28, "spi:description": "LOGCAPV Philippines", "spi:changeby": "KUBE920000027", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTEdDUEg-", "spi:enterdate": "2022-08-03T08:15:13+00:00", "spi:enterby": "PALA383021", "_rowstamp": "474859776", "spi:active": true, "spi:contact": "MAXW70947", "spi:changedate": "2022-11-07T10:14:07+00:00", "spi:plusgopenomuid": 25, "spi:systemid": "PHILLOC", "spi:siteid": "LGCPH", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "2310559710", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28/billtoshipto/0-86", "spi:addresscode": "110134", "spi:billtoshiptoid": 86, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEzNC9VU0FSTVkvTEdDUEg-"}, {"spi:shiptodefault": false, "_rowstamp": "2310559711", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28/billtoshipto/1-52", "spi:addresscode": "23101", "spi:billtoshiptoid": 52, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIzMTAxL1VTQVJNWS9MR0NQSA--"}, {"spi:shiptodefault": false, "_rowstamp": "*********", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28/billtoshipto/2-53", "spi:addresscode": "23102", "spi:billtoshiptoid": 53, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIzMTAyL1VTQVJNWS9MR0NQSA--"}, {"spi:shiptodefault": false, "_rowstamp": "*********", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28/billtoshipto/3-51", "spi:addresscode": "23199", "spi:billtoshiptoid": 51, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIzMTk5L1VTQVJNWS9MR0NQSA--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30", "spi:siteuid": 30, "spi:description": "PMO Site", "spi:changeby": "PALA383021", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTEdDQVA-", "spi:enterdate": "2022-12-14T14:16:44+00:00", "spi:enterby": "PALA383021", "_rowstamp": "518134733", "spi:active": true, "spi:contact": "BILL.COOLER", "spi:changedate": "2022-12-15T08:15:12+00:00", "spi:plusgopenomuid": 27, "spi:siteid": "LGCAP", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "1959066539", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30/billtoshipto/0-59", "spi:addresscode": "11001", "spi:billtoshiptoid": 59, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDAxL1VTQVJNWS9MR0NBUA--"}, {"spi:shiptodefault": true, "_rowstamp": "1959066540", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30/billtoshipto/1-77", "spi:addresscode": "11013", "spi:billtoshiptoid": 77, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEzL1VTQVJNWS9MR0NBUA--"}, {"spi:shiptodefault": false, "_rowstamp": "518481100", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30/billtoshipto/2-60", "spi:addresscode": "11073", "spi:billtoshiptoid": 60, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDczL1VTQVJNWS9MR0NBUA--"}, {"spi:shiptodefault": false, "_rowstamp": "518481101", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30/billtoshipto/3-61", "spi:addresscode": "11099", "spi:billtoshiptoid": 61, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk5L1VTQVJNWS9MR0NBUA--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/8-33", "spi:siteuid": 33, "spi:description": "LOGCAP Jordan", "spi:changeby": "KRISH113979", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQ0pPUkQ-", "spi:enterdate": "2024-07-12T14:55:02+00:00", "spi:enterby": "KRISH113979", "_rowstamp": "1622341852", "spi:active": true, "spi:changedate": "2024-07-12T14:55:51+00:00", "spi:plusgopenomuid": 30, "spi:systemid": "JORDANLOC", "spi:siteid": "CJORD", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "1861821384", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/8-33/billtoshipto/0-74", "spi:addresscode": "110125", "spi:billtoshiptoid": 74, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEyNS9VU0FSTVkvQ0pPUkQ-"}, {"spi:shiptodefault": false, "_rowstamp": "1809182203", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/8-33/billtoshipto/1-72", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 72, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNBUk1ZL0NKT1JE"}]}], "spi:itemsetid": "ITEMSET", "spi:description": "United States Army", "spi:dfltitemstatus_description": "Active", "spi:dfltitemstatus": "ACTIVE", "spi:orgid": "USARMY", "spi:address": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/0-50", "spi:addressid": 50, "spi:description": "UNITED STATES", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMDEvVVNBUk1Z", "_rowstamp": "517282121", "spi:addresscode": "11001", "spi:address5": "US - UNITED STATES", "spi:address4": "80919", "spi:address3": "Colorado Springs, CO", "spi:changedate": "2022-12-14T14:18:29+00:00", "spi:address2": "Suite 300", "spi:address1": "2424 Garden of the Gods Road"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/1-39", "spi:addressid": 39, "spi:description": "Vectrus Systems", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTAwL1VTQVJNWQ--", "_rowstamp": "1145873275", "spi:addresscode": "110100", "spi:address5": "US - UNITED STATES", "spi:address4": "96853", "spi:address3": "HI", "spi:changedate": "2024-02-12T15:30:06+00:00", "spi:address2": "Honolulu", "spi:address1": "Building 2116 Engine Test Road Hickam AFB"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/2-60", "spi:addressid": 60, "spi:description": "ARMY CONTRACTING COMMAND - ROCK ISL", "spi:changeby": "KRISH113979", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTEzL1VTQVJNWQ--", "_rowstamp": "1706523608", "spi:addresscode": "110113", "spi:address5": "US", "spi:address4": "61299", "spi:address3": "IL", "spi:changedate": "2024-07-17T06:47:51+00:00", "spi:address2": "Rock Island", "spi:address1": "3055 RODMAN AVE"}, {"_rowstamp": "1861820634", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/3-62", "spi:addressid": 62, "spi:description": "V2X Operations", "spi:addresscode": "110125", "spi:address5": "JO - Jordan", "spi:address4": "09315", "spi:changeby": "MOOR382170", "spi:address3": "APO, AE", "spi:changedate": "2024-08-02T18:17:31+00:00", "spi:address1": "KFAB, Jordan", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTI1L1VTQVJNWQ--"}, {"_rowstamp": "98392304", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/4-17", "spi:addressid": 17, "spi:description": "Central Material Warehouse - <PERSON>fjan", "spi:addresscode": "11013", "spi:address5": "Kuwait", "spi:changeby": "KANJ125715", "spi:address3": "<PERSON><PERSON><PERSON>", "spi:changedate": "2021-09-08T16:47:43+00:00", "spi:address2": "Camp <PERSON>jan", "spi:address1": "PWD T-3, ZONE-6", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTMvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/5-70", "spi:addressid": 70, "spi:description": "Samandra Office - Philippines", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTM0L1VTQVJNWQ--", "_rowstamp": "2310557083", "spi:addresscode": "110134", "spi:address5": "Philippines", "spi:address4": "80949", "spi:address3": "Brgy. Cawag, Subic Bay Freeport Zone 2222", "spi:changedate": "2025-03-03T14:42:56+00:00", "spi:address2": "Redondo Peninsula, Sitio, Agusuhin", "spi:address1": "Greenbeach1"}, {"_rowstamp": "258295275", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/6-36", "spi:addressid": 36, "spi:description": "Iraq Ship To Al Asad", "spi:addresscode": "11039", "spi:address5": "Iraq", "spi:address4": "09333 - APO", "spi:changeby": "SHIV125743", "spi:changedate": "2022-04-06T05:55:14+00:00", "spi:address2": "Camp Al Asad, IRAQ", "spi:address1": "Vectrus Materials", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMzkvVVNBUk1Z"}, {"_rowstamp": "98392305", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/7-18", "spi:addressid": 18, "spi:description": "Vectrus Systems Corporation/KBOSSS", "spi:addresscode": "11054", "spi:address5": "Kuwait", "spi:changeby": "KANJ125715", "spi:address3": "<PERSON><PERSON><PERSON>", "spi:changedate": "2021-09-08T16:48:10+00:00", "spi:address2": "Camp <PERSON>jan", "spi:address1": "PMT/RIP, APO, AE  09366", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNTQvVVNBUk1Z"}, {"_rowstamp": "331751951", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/8-19", "spi:addressid": 19, "spi:description": "Vectrus Systems Corporation", "spi:addresscode": "11073", "spi:address5": "Kuwait", "spi:changeby": "SHIV125743", "spi:address3": "Camp <PERSON>jan", "spi:changedate": "2022-06-23T14:06:40+00:00", "spi:address2": "Camp <PERSON>jan", "spi:address1": "U.S. MILITARY CRSP, BLK Tent, Lot 80, Zone2", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNzMvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/9-37", "spi:addressid": 37, "spi:description": "Iraq Ship To - Erbil", "spi:changeby": "SHIV125743", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwODQvVVNBUk1Z", "_rowstamp": "258296238", "spi:addresscode": "11084", "spi:address5": "IRAQ", "spi:address4": "09316 - APO", "spi:address3": "AE", "spi:changedate": "2022-04-06T05:56:11+00:00", "spi:address2": "Camp Erbil Air Base, IRAQ", "spi:address1": "Vectrus Materials"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/10-40", "spi:addressid": 40, "spi:description": "Oakland Cross-Dock", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwODkvVVNBUk1Z", "_rowstamp": "1145751690", "spi:addresscode": "11089", "spi:address5": "US - UNITED STATES", "spi:address4": "94603", "spi:address3": "CA", "spi:changedate": "2024-02-12T14:03:15+00:00", "spi:address2": "Oakland", "spi:address1": "9757 San Leandro St"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/11-46", "spi:addressid": 46, "spi:description": "ARMY CONTRACTING COMMAND", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwOTUvVVNBUk1Z", "_rowstamp": "470133418", "spi:addresscode": "11095", "spi:address5": "US - UNITED STATES", "spi:address4": "61299", "spi:address3": "IL", "spi:changedate": "2022-11-02T15:30:46+00:00", "spi:address2": "ROCK ISLAND", "spi:address1": "BLDGS 60 & 62"}, {"_rowstamp": "1525700155", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/12-35", "spi:addressid": 35, "spi:description": "Qatar", "spi:addresscode": "11098", "spi:changeby": "MOOR382170", "spi:address3": "Qatar", "spi:changedate": "2024-07-01T13:55:15+00:00", "spi:address2": "C.A.R.<PERSON>", "spi:address1": "Bldg 110 - Materials Warehouse Receiving Section", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwOTgvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/13-41", "spi:addressid": 41, "spi:description": "<EMAIL>", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwOTkvVVNBUk1Z", "_rowstamp": "**********", "spi:addresscode": "11099", "spi:address5": "US", "spi:address4": "Accounts Payable Box 4 PO Box 39710", "spi:address3": "OR Vectrus Systems Corporation", "spi:changedate": "2025-01-10T20:25:50+00:00", "spi:address2": "<EMAIL>", "spi:address1": "<EMAIL>"}, {"_rowstamp": "*********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/14-43", "spi:addressid": 43, "spi:description": "Philippines", "spi:addresscode": "23101", "spi:address5": "Philippines", "spi:changeby": "PALA383021", "spi:changedate": "2022-08-03T08:17:40+00:00", "spi:address2": "Manila", "spi:address1": "American/U.S. Embassy", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjMxMDEvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/15-44", "spi:addressid": 44, "spi:description": "Samandra Office - Philippines", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjMxMDIvVVNBUk1Z", "_rowstamp": "*********", "spi:addresscode": "23102", "spi:address5": "Philippines", "spi:address4": "80949", "spi:address3": "Brgy. Cawag, Subic Bay Freeport Zone 2222", "spi:changedate": "2022-08-03T08:19:31+00:00", "spi:address2": "Redondo Peninsula, Sitio, Agusuhin", "spi:address1": "Greenbeach1"}, {"_rowstamp": "*********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/16-42", "spi:addressid": 42, "spi:description": "Accounts Payable Box 4", "spi:addresscode": "23199", "spi:address5": "US - United States", "spi:changeby": "PALA383021", "spi:address3": "CO", "spi:changedate": "2022-08-03T08:16:53+00:00", "spi:address2": "80949 - Colorado Springs", "spi:address1": "PO Box 39710", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjMxOTkvVVNBUk1Z"}, {"_rowstamp": "*********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/17-47", "spi:addressid": 47, "spi:description": "454Garden City Soi", "spi:addresscode": "24001", "spi:address5": "TH - THAILAND", "spi:changeby": "PALA383021", "spi:address3": "Phrakanong Nua, Wattana", "spi:changedate": "2022-11-02T15:31:41+00:00", "spi:address2": "Ni<PERSON><PERSON>", "spi:address1": "<PERSON><PERSON><PERSON><PERSON><PERSON> 79", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjQwMDEvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/18-48", "spi:addressid": 48, "spi:description": "Army Aviation Centre 303", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjQwMDIvVVNBUk1Z", "_rowstamp": "470134799", "spi:addresscode": "24002", "spi:address5": "TH - THAILAND", "spi:address4": "Lopburi", "spi:address3": "15160 - <PERSON><PERSON>", "spi:changedate": "2022-11-02T15:32:43+00:00", "spi:address2": "Village #7, Sub-district: Khao P, 15160 - Muang", "spi:address1": "Hangar 277"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/19-49", "spi:addressid": 49, "spi:description": "PO Box 39710", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjQwOTkvVVNBUk1Z", "_rowstamp": "*********", "spi:addresscode": "24099", "spi:address5": "US - United States", "spi:address4": "80949", "spi:address3": "CO", "spi:changedate": "2022-11-02T15:33:39+00:00", "spi:address2": "Colorado Springs", "spi:address1": "Accounts Payable Box 4"}, {"_rowstamp": "********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/20-10", "spi:addressid": 10, "spi:description": "Erbil Air Base", "spi:addresscode": "EAB", "spi:address5": "Iraq", "spi:changeby": "SHIV125743", "spi:changedate": "2021-04-15T14:36:32+00:00", "spi:address1": "CRSP Yard, Attn: Vectrus / LOGCAP, Erbil Air Base", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvRUFCL1VTQVJNWQ--"}, {"_rowstamp": "*********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/21-38", "spi:addressid": 38, "spi:description": "APO <PERSON>", "spi:addresscode": "KWAJ", "spi:changeby": "KRISH113979", "spi:changedate": "2022-05-07T02:35:56+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvS1dBSi9VU0FSTVk-"}, {"_rowstamp": "********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/22-9", "spi:addressid": 9, "spi:description": "Thailand Default Bill to Address", "spi:addresscode": "LCVT", "spi:address5": "Thailand", "spi:changeby": "SHIV125743", "spi:changedate": "2021-01-21T09:09:53+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvTENWVC9VU0FSTVk-"}, {"_rowstamp": "532352469", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/23-55", "spi:addressid": 55, "spi:description": "Vectrus/Supply", "spi:addresscode": "LCVT APO", "spi:address4": "96502", "spi:changeby": "MOOR382170", "spi:address3": "AP", "spi:changedate": "2022-12-28T19:50:46+00:00", "spi:address2": "APO", "spi:address1": "Unit 45201", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvTENWVCBBUE8vVVNBUk1Z"}, {"_rowstamp": "2532097", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/24-1", "spi:addressid": 1, "spi:description": "Qatar Base Opeartions, Support and Service", "spi:addresscode": "QBOSS", "spi:address5": "Qatar", "spi:changeby": "PRABHAK", "spi:address3": "Qatar", "spi:changedate": "2020-05-22T12:53:41+00:00", "spi:address2": "Doha", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvUUJPU1MvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/25-16", "spi:addressid": 16, "spi:description": "Vectrus Systems", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvVkVDVFJVUy9VU0FSTVk-", "_rowstamp": "**********", "spi:addresscode": "VECTRUS", "spi:address5": "Colorado Springs, CO 80949 USA", "spi:address4": "Accounts Payable Box 4 PO Box 39710", "spi:address3": "OR Vectrus Systems Corporation", "spi:changedate": "2025-01-10T20:26:33+00:00", "spi:address2": "<EMAIL>", "spi:address1": "<EMAIL>"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z", "spi:enterdate": "2020-05-22T09:20:35+00:00", "spi:enterby": "KRISHNAMURTHYP", "spi:plusgaddassetspec": true, "_rowstamp": "********", "spi:category": "STK", "spi:clearingacct": "000000.0000.00000", "spi:companysetid": "COMPSET", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 1, "spi:basecurrency1": "USD"}, {"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3", "spi:siteuid": 3, "spi:description": "Romania Base Operations Support & Service Bridge", "spi:changeby": "KUBE920000027", "spi:vecfreight": "10", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvUkJPU1M-", "spi:enterdate": "2020-07-27T16:00:34+00:00", "spi:enterby": "AKANJILAL", "_rowstamp": "*********", "spi:active": true, "spi:contact": "SING364563", "spi:changedate": "2022-11-07T10:14:29+00:00", "spi:plusgopenomuid": 3, "spi:systemid": "ROMLOC1", "spi:siteid": "RBOSS", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "1959078227", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/0-62", "spi:addresscode": "11001", "spi:billtoshiptoid": 62, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDAxL1VTTkFWWS9SQk9TUw--"}, {"spi:shiptodefault": false, "_rowstamp": "644166180", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/1-67", "spi:addresscode": "110107", "spi:billtoshiptoid": 67, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEwNy9VU05BVlkvUkJPU1M-"}, {"spi:shiptodefault": true, "_rowstamp": "615476875", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/2-63", "spi:addresscode": "11065", "spi:billtoshiptoid": 63, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDY1L1VTTkFWWS9SQk9TUw--"}, {"spi:shiptodefault": false, "_rowstamp": "643995556", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/3-10", "spi:addresscode": "DEFAULT", "spi:billtoshiptoid": 10, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL0RFRkFVTFQvVVNOQVZZL1JCT1NT"}, {"spi:shiptodefault": false, "_rowstamp": "643995555", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/4-3", "spi:shiptocontact": "LAND355149", "spi:addresscode": "RBOSS", "spi:billtocontact": "LAND355149", "spi:billtoshiptoid": 3, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1JCT1NTL1VTTkFWWS9SQk9TUw--"}, {"spi:shiptodefault": false, "_rowstamp": "643995557", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/5-11", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 11, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNOQVZZL1JCT1NT"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/1-7", "spi:siteuid": 7, "spi:description": "United States Army (GCSMAC)", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvVVNBUg--", "spi:enterdate": "2020-10-23T10:19:52+00:00", "spi:enterby": "KANJ125715", "_rowstamp": "474860355", "spi:active": true, "spi:contact": "PETE74683", "spi:changedate": "2022-11-07T10:14:32+00:00", "spi:plusgopenomuid": 7, "spi:systemid": "SHAFLOC", "spi:siteid": "USAR", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "28530209", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/1-7/billtoshipto/0-13", "spi:shiptocontact": "PETE74683", "spi:addresscode": "USAR", "spi:billtocontact": "PETE74683", "spi:billtoshiptoid": 13, "spi:billto": true, "spi:shipto": true, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1VTQVIvVVNOQVZZL1VTQVI-"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/2-9", "spi:siteuid": 9, "spi:description": "ISA Bahrain", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQkJPUw--", "spi:enterdate": "2020-11-27T09:04:04+00:00", "spi:enterby": "SHIV125743", "_rowstamp": "474860356", "spi:active": true, "spi:contact": "SING364563", "spi:changedate": "2022-11-07T10:14:34+00:00", "spi:plusgopenomuid": 9, "spi:systemid": "BBOSLOC", "spi:siteid": "BBOS", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "55628968", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/2-9/billtoshipto/0-20", "spi:addresscode": "11043", "spi:billtoshiptoid": 20, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDQzL1VTTkFWWS9CQk9T"}, {"spi:shiptodefault": false, "_rowstamp": "55628969", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/2-9/billtoshipto/1-21", "spi:addresscode": "11097", "spi:billtoshiptoid": 21, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk3L1VTTkFWWS9CQk9T"}, {"spi:shiptodefault": false, "_rowstamp": "55643177", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/2-9/billtoshipto/2-15", "spi:addresscode": "BBOS", "spi:billtoshiptoid": 15, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL0JCT1MvVVNOQVZZL0JCT1M-"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/3-13", "spi:siteuid": 13, "spi:description": "Naval Station Guantanamo bay", "spi:changeby": "MOOR382170", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTlNHQkE-", "spi:enterdate": "2021-07-07T14:42:44+00:00", "spi:enterby": "SHIV125743", "_rowstamp": "1973957508", "spi:active": true, "spi:contact": "TOLE358169", "spi:changedate": "2024-10-01T12:17:58+00:00", "spi:plusgopenomuid": 12, "spi:systemid": "NSGBALOC", "spi:siteid": "NSGBA", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "1887465229", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/3-13/billtoshipto/0-75", "spi:addresscode": "110126", "spi:billtoshiptoid": 75, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEyNi9VU05BVlkvTlNHQkE-"}, {"spi:shiptodefault": true, "_rowstamp": "2299135065", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/3-13/billtoshipto/1-78", "spi:shiptocontact": "ANGL3835206", "spi:addresscode": "110132", "spi:billtoshiptoid": 78, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEzMi9VU05BVlkvTlNHQkE-"}, {"spi:shiptodefault": false, "_rowstamp": "2299135066", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/3-13/billtoshipto/2-22", "spi:addresscode": "11023", "spi:billtoshiptoid": 22, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDIzL1VTTkFWWS9OU0dCQQ--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/4-35", "spi:siteuid": 35, "spi:description": "Poland Base Operations Support & Service", "spi:changeby": "KRISH113979", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvUEJPUw--", "spi:enterdate": "2025-01-28T10:41:45+00:00", "spi:enterby": "KRISH113979", "_rowstamp": "2232929960", "spi:active": true, "spi:changedate": "2025-01-28T10:42:03+00:00", "spi:plusgopenomuid": 32, "spi:systemid": "PBOSLOC", "spi:siteid": "PBOS", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "2275320652", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/4-35/billtoshipto/0-84", "spi:addresscode": "11001", "spi:billtoshiptoid": 84, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDAxL1VTTkFWWS9QQk9T"}, {"spi:shiptodefault": true, "_rowstamp": "2275320655", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/4-35/billtoshipto/1-85", "spi:addresscode": "20008", "spi:billtoshiptoid": 85, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMDA4L1VTTkFWWS9QQk9T"}]}], "spi:itemsetid": "ITEMSET1", "spi:description": "United States Navy", "spi:dfltitemstatus_description": "Active", "spi:dfltitemstatus": "ACTIVE", "spi:orgid": "USNAVY", "spi:address": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/0-53", "spi:addressid": 53, "spi:description": "UNITED STATES", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMDEvVVNOQVZZ", "_rowstamp": "1894146681", "spi:addresscode": "11001", "spi:address5": "US - UNITED STATES", "spi:address4": "80919", "spi:address3": "Colorado Springs, CO", "spi:changedate": "2024-08-20T20:31:36+00:00", "spi:address2": "2424 Garden of the Gods Road Suite 300", "spi:address1": "Vectrus Systems LLC"}, {"_rowstamp": "644165534", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/1-56", "spi:addressid": 56, "spi:description": "RBOSS FPO Shipping address", "spi:addresscode": "110107", "spi:address4": "09712", "spi:changeby": "MOOR382170", "spi:address3": "AE", "spi:changedate": "2023-03-28T18:04:45+00:00", "spi:address2": "FPO", "spi:address1": "PSC 825 BOX 212", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTA3L1VTTkFWWQ--"}, {"_rowstamp": "1887464857", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/2-63", "spi:addressid": 63, "spi:description": "IBC Airways c/o Vectrus", "spi:addresscode": "110126", "spi:address4": "33166", "spi:changeby": "MOOR382170", "spi:address3": "FL", "spi:changedate": "2024-08-16T16:49:16+00:00", "spi:address2": "Miami", "spi:address1": "IBC Airways Inc 5600 NW 36 Street", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTI2L1VTTkFWWQ--"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/3-65", "spi:addressid": 65, "spi:description": "1st Coast Cargo, Inc c/o V2X", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTMyL1VTTkFWWQ--", "_rowstamp": "2033837343", "spi:addresscode": "110132", "spi:address5": "US", "spi:address4": "32218", "spi:address3": "FL", "spi:changedate": "2024-10-29T19:54:34+00:00", "spi:address2": "Jacksonville", "spi:address1": "1400 Eastport Road"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/4-15", "spi:addressid": 15, "spi:description": "Naval Station Guantanamo bay", "spi:changeby": "SHIV125743", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMjMvVVNOQVZZ", "_rowstamp": "75260488", "spi:addresscode": "11023", "spi:address5": "USA", "spi:address4": "32254", "spi:address3": "FL", "spi:changedate": "2021-07-07T14:42:35+00:00", "spi:address2": "JACKSONVILLE", "spi:address1": "VECTRUS SYSTEMS CORPORATION PO# XXXXXX 6801 W 12th"}, {"_rowstamp": "55628705", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/5-13", "spi:addressid": 13, "spi:description": "U.S. Navy: Sheik ISA Air Base", "spi:addresscode": "11043", "spi:address4": "09859", "spi:changeby": "TOMY125770", "spi:address3": "AE", "spi:changedate": "2021-04-16T14:52:15+00:00", "spi:address2": "FPO", "spi:address1": "Vectrus Materials Building 8100", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNDMvVVNOQVZZ"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/6-54", "spi:addressid": 54, "spi:description": "US Navy Facility", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNjUvVVNOQVZZ", "_rowstamp": "644163095", "spi:addresscode": "11065", "spi:address5": "Romania", "spi:address4": "237130", "spi:address3": "Judet Olt", "spi:changedate": "2023-03-28T18:01:43+00:00", "spi:address2": "<PERSON><PERSON><PERSON>", "spi:address1": "Unitatea Militara 01871, str. <PERSON><PERSON> 17"}, {"_rowstamp": "********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/7-14", "spi:addressid": 14, "spi:description": "Vectrus Systems Corporation", "spi:addresscode": "11097", "spi:address4": "80449", "spi:changeby": "TOMY125770", "spi:address3": "CO", "spi:changedate": "2021-04-16T14:52:51+00:00", "spi:address2": "Colorado Springs", "spi:address1": "PO Box 39710, Accounts Payable Box 4", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwOTcvVVNOQVZZ"}, {"_rowstamp": "**********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/8-69", "spi:addressid": 69, "spi:description": "Naval Support Facility (NSF)", "spi:addresscode": "20008", "spi:changeby": "MOOR382170", "spi:address3": "PL - POLAND", "spi:changedate": "2025-04-22T12:22:12+00:00", "spi:address2": "Redzikowo", "spi:address1": "Naval Support Facility (NSF)", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAwMDgvVVNOQVZZ"}, {"_rowstamp": "********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/9-8", "spi:addressid": 8, "spi:description": "Bahrain Default Bill to Address", "spi:addresscode": "BBOS", "spi:address5": "BAHRAIN", "spi:address4": "09859", "spi:changeby": "SHIV125743", "spi:changedate": "2020-11-27T09:02:20+00:00", "spi:address2": "FPO, AE 09859", "spi:address1": "ISA BAHRAIN US NAVY, VECTRUS MATERIALS BAHRAIN", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvQkJPUy9VU05BVlk-"}, {"_rowstamp": "12447714", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/10-5", "spi:addressid": 5, "spi:description": "Default Shipping Address", "spi:addresscode": "DEFAULT", "spi:changeby": "JACOB.GEORGE", "spi:changedate": "2020-07-30T06:58:47+00:00", "spi:address2": "FPO AE 09712-0005", "spi:address1": "PSC 825 BOX 212", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvREVGQVVMVC9VU05BVlk-"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/11-3", "spi:addressid": 3, "spi:description": "Romania Base Operations Support & Service Bridge", "spi:changeby": "AKANJILAL", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvUkJPU1MvVVNOQVZZ", "_rowstamp": "11849000", "spi:addresscode": "RBOSS", "spi:address5": "Romania", "spi:address4": "235200", "spi:address3": "Olt County", "spi:changedate": "2020-07-27T16:00:18+00:00", "spi:address2": "<PERSON><PERSON><PERSON>", "spi:address1": "<PERSON><PERSON><PERSON><PERSON>, US NATO Facility UM 01871"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/12-7", "spi:addressid": 7, "spi:description": "USAR De<PERSON>ult Bill to Address", "spi:changeby": "KANJ125715", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvVVNBUi9VU05BVlk-", "_rowstamp": "24348459", "spi:addresscode": "USAR", "spi:address5": "USA", "spi:address4": "80919", "spi:address3": "Colorado", "spi:changedate": "2020-10-23T10:16:51+00:00", "spi:address2": "Colorado Springs", "spi:address1": "2424 West Garden of the Gods Rd, Suite 300 Bldg. E"}, {"_rowstamp": "12447740", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/13-6", "spi:addressid": 6, "spi:description": "<PERSON><PERSON><PERSON><PERSON>", "spi:addresscode": "VECTRUS", "spi:address5": "Romania", "spi:changeby": "JACOB.GEORGE", "spi:address3": "Jud. Olt. 235200", "spi:changedate": "2020-07-30T06:58:58+00:00", "spi:address2": "<PERSON><PERSON><PERSON>", "spi:address1": "US NATO FACILITY UM 01871", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvVkVDVFJVUy9VU05BVlk-"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ", "spi:enterdate": "2020-07-27T15:59:15+00:00", "spi:enterby": "AKANJILAL", "spi:plusgaddassetspec": true, "_rowstamp": "54134892", "spi:category": "STK", "spi:clearingacct": "000000.0000.00000", "spi:companysetid": "COMPSET1", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 6, "spi:basecurrency1": "USD"}, {"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32", "spi:siteuid": 32, "spi:description": "DLA", "spi:changeby": "MOOR382170", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvRExB", "spi:enterdate": "2024-04-25T13:59:38+00:00", "spi:enterby": "MOOR382170", "_rowstamp": "1273477577", "spi:active": true, "spi:changedate": "2024-04-25T14:01:19+00:00", "spi:plusgopenomuid": 29, "spi:siteid": "DLA", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "1935002149", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/0-76", "spi:shiptocontact": "CHIL391671", "spi:addresscode": "20052", "spi:billtoshiptoid": 76, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMDUyL1ZTRS9ETEE-"}, {"spi:shiptodefault": true, "_rowstamp": "1353929377", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/1-69", "spi:addresscode": "20098", "spi:billtoshiptoid": 69, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMDk4L1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "1372034516", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/2-70", "spi:addresscode": "20099", "spi:billtocontact": "<EMAIL>", "spi:billtoshiptoid": 70, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMDk5L1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "2201760343", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/3-79", "spi:addresscode": "20102", "spi:billtoshiptoid": 79, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMTAyL1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "2223472564", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/4-80", "spi:addresscode": "20103", "spi:billtoshiptoid": 80, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMTAzL1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "2312912121", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/5-87", "spi:addresscode": "20104", "spi:billtoshiptoid": 87, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMTA0L1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "2253030898", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/6-81", "spi:addresscode": "201104", "spi:billtoshiptoid": 81, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMTEwNC9WU0UvRExB"}]}], "spi:itemsetid": "ITEMSET3", "spi:description": "VSE", "spi:dfltitemstatus_description": "Active", "spi:dfltitemstatus": "ACTIVE", "spi:orgid": "VSE", "spi:address": [{"_rowstamp": "1936777684", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/0-64", "spi:addressid": 64, "spi:description": "<PERSON> Childers", "spi:addresscode": "20052", "spi:changeby": "MOOR382170", "spi:changedate": "2024-09-11T19:05:29+00:00", "spi:address2": "<EMAIL>", "spi:address1": "Contact Sierra Childers prior to shipping", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAwNTIvVlNF"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/1-58", "spi:addressid": 58, "spi:description": "V2X", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAwOTgvVlNF", "_rowstamp": "1372375898", "spi:addresscode": "20098", "spi:address5": "US", "spi:address4": "23323", "spi:address3": "VA", "spi:changedate": "2024-05-31T19:24:43+00:00", "spi:address2": "Chesapeake", "spi:address1": "713 Fenway Ave. Suite B"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/2-59", "spi:addressid": 59, "spi:description": "Vectrus Systems Corporation", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAwOTkvVlNF", "_rowstamp": "**********", "spi:addresscode": "20099", "spi:address5": "US", "spi:address4": "80949", "spi:address3": "CO", "spi:changedate": "2024-05-28T12:17:25+00:00", "spi:address2": "Colorado Springs", "spi:address1": "PO Box 39710 Accounts Payable Box 4"}, {"_rowstamp": "**********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/3-66", "spi:addressid": 66, "spi:description": "Heale Manufacturing Co.", "spi:addresscode": "20102", "spi:address4": "53186", "spi:changeby": "MOOR382170", "spi:address3": "WI", "spi:changedate": "2025-01-14T19:54:13+00:00", "spi:address2": "<PERSON><PERSON><PERSON><PERSON>", "spi:address1": "1231 The Strand", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAxMDIvVlNF"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/4-67", "spi:addressid": 67, "spi:description": "Advance Design & Manufacturing Corp.", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAxMDMvVlNF", "_rowstamp": "**********", "spi:addresscode": "20103", "spi:address5": "US", "spi:address4": "22312", "spi:address3": "VA", "spi:changedate": "2025-01-24T11:43:05+00:00", "spi:address2": "Alenandria", "spi:address1": "6460 A General Green Way"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/5-71", "spi:addressid": 71, "spi:description": "Quality Support Attn:<PERSON>", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAxMDQvVlNF", "_rowstamp": "2312909367", "spi:addresscode": "20104", "spi:address5": "US", "spi:address4": "15683", "spi:address3": "PA", "spi:changedate": "2025-03-04T15:07:20+00:00", "spi:address2": "Scottdale", "spi:address1": "900 Water Street"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/6-68", "spi:addressid": 68, "spi:description": "Valence Surface Technologies/H&W Global Industries", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAxMTA0L1ZTRQ--", "_rowstamp": "2253029471", "spi:addresscode": "201104", "spi:address5": "US", "spi:address4": "15717", "spi:address3": "PA", "spi:changedate": "2025-02-05T19:01:32+00:00", "spi:address2": "Blairsville", "spi:address1": "414 Innovation Drive"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF", "spi:enterdate": "2024-04-25T13:57:47+00:00", "spi:enterby": "MOOR382170", "spi:plusgaddassetspec": true, "_rowstamp": "1273476935", "spi:category": "STK", "spi:clearingacct": "000000.000000.000000", "spi:companysetid": "COMPSET3", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 10, "spi:basecurrency1": "USD"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization", "member": [{"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_T01EQUM-/site/0-34", "spi:siteuid": 34, "spi:description": "OMDAC Program", "spi:changeby": "KRISH113979", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvT01EQUNQUkc-", "spi:enterdate": "2025-01-10T14:21:41+00:00", "spi:enterby": "KRISH113979", "_rowstamp": "2193387237", "spi:active": true, "spi:contact": "HAMP116428", "spi:changedate": "2025-01-10T14:21:55+00:00", "spi:plusgopenomuid": 31, "spi:systemid": "OMDACLOC", "spi:siteid": "OMDACPRG"}], "spi:itemsetid": "ITEMSET4", "spi:description": "OMDAC Programs", "spi:dfltitemstatus_description": "Pending", "spi:dfltitemstatus": "PENDING", "spi:orgid": "OMDAC", "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_T01EQUM-", "spi:enterdate": "2025-01-10T14:17:32+00:00", "spi:enterby": "KRISH113979", "spi:plusgaddassetspec": true, "_rowstamp": "2193386143", "spi:category": "STK", "spi:clearingacct": "0000.0000.0000", "spi:companysetid": "COMPSET4", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 12, "spi:basecurrency1": "USD"}, {"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16", "spi:siteuid": 16, "spi:description": "Thule Air Base", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvVEhVTEU-", "spi:enterdate": "2021-09-27T02:12:17+00:00", "spi:enterby": "DATASPLICE", "_rowstamp": "*********", "spi:active": true, "spi:contact": "WILLIAM.WOMACK", "spi:changedate": "2022-11-07T10:15:45+00:00", "spi:plusgopenomuid": 14, "spi:systemid": "THULELOC", "spi:siteid": "THULE", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "*********", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/0-31", "spi:addresscode": "30001", "spi:billtoshiptoid": 31, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDAxL1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "408469260", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/1-29", "spi:addresscode": "30003", "spi:billtoshiptoid": 29, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDAzL1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583525", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/2-30", "spi:addresscode": "30004", "spi:billtoshiptoid": 30, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA0L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583527", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/3-32", "spi:addresscode": "30005", "spi:billtoshiptoid": 32, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA1L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583528", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/4-33", "spi:addresscode": "30006", "spi:billtoshiptoid": 33, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA2L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583529", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/5-34", "spi:addresscode": "30007", "spi:billtoshiptoid": 34, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA3L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": true, "_rowstamp": "115583808", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/6-35", "spi:addresscode": "30008", "spi:billtoshiptoid": 35, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA4L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "408470027", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/7-54", "spi:addresscode": "30009", "spi:billtoshiptoid": 54, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzMwMDA5L1VTQUYvVEhVTEU-"}, {"spi:shiptodefault": false, "_rowstamp": "115583809", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/8-27", "spi:addresscode": "MCGUIRE AFB - US", "spi:billtoshiptoid": 27, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL01DR1VJUkUgQUZCIC0gVVMvVVNBRi9USFVMRQ--"}, {"spi:shiptodefault": false, "_rowstamp": "408470028", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/0-16/billtoshipto/9-28", "spi:addresscode": "<EMAIL>", "spi:billtoshiptoid": 28, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVNJTlZPSUNFU0BWRUNUUlVTLkNPTS9VU0FGL1RIVUxF"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/1-24", "spi:siteuid": 24, "spi:description": "<PERSON>ppard Air Force Base", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvU0hFUEQ-", "spi:enterdate": "2022-01-20T16:48:35+00:00", "spi:enterby": "JAIN383011", "_rowstamp": "474861216", "spi:active": true, "spi:changedate": "2022-11-07T10:15:49+00:00", "spi:plusgopenomuid": 22, "spi:systemid": "SHEPDLOC", "spi:siteid": "SHEPD", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "190260395", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/site/1-24/billtoshipto/0-37", "spi:addresscode": "11072", "spi:billtoshiptoid": 37, "spi:billto": true, "spi:shipto": true, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDcyL1VTQUYvU0hFUEQ-"}]}], "spi:itemsetid": "ITEMSET2", "spi:description": "United States Air Force", "spi:dfltitemstatus_description": "Active", "spi:dfltitemstatus": "ACTIVE", "spi:orgid": "USAF", "spi:address": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/0-33", "spi:addressid": 33, "spi:description": "Sheppard AFB", "spi:changeby": "JAIN383011", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNzIvVVNBRg--", "_rowstamp": "190260224", "spi:addresscode": "11072", "spi:address5": "US", "spi:address4": "76311", "spi:address3": "TX", "spi:changedate": "2022-01-20T16:50:34+00:00", "spi:address2": "Wichita Falls", "spi:address1": "235 9th Avenue Bldg. 1404"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/1-20", "spi:addressid": 20, "spi:description": "Vectrus HQ", "spi:changeby": "KRISH113979", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDEvVVNBRg--", "_rowstamp": "115580885", "spi:addresscode": "30001", "spi:address5": "US", "spi:address4": "80919", "spi:address3": "CO", "spi:changedate": "2021-09-27T03:11:36+00:00", "spi:address2": "Colorado Springs", "spi:address1": "2424 Garden of the Gods Road, Suite 300"}, {"_rowstamp": "290240184", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/2-21", "spi:addressid": 21, "spi:description": "Vectrus Services A/S", "spi:addresscode": "30003", "spi:address5": "DK", "spi:address4": "2100", "spi:changeby": "PALA383021", "spi:changedate": "2022-05-10T04:17:05+00:00", "spi:address2": "Copenhagen", "spi:address1": "Vectrus Services, Indiavej 1,4", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDMvVVNBRg--"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/3-22", "spi:addressid": 22, "spi:description": "NORFOLK", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDQvVVNBRg--", "_rowstamp": "290240707", "spi:addresscode": "30004", "spi:address5": "US", "spi:address4": "23511", "spi:address3": "VA", "spi:changedate": "2022-05-10T04:18:01+00:00", "spi:address2": "NORFOLK", "spi:address1": "Pacer Goose, Intermodal Hub, 9551 Decatur Ave"}, {"_rowstamp": "115581388", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/4-23", "spi:addressid": 23, "spi:description": "Schenker A/S", "spi:addresscode": "30005", "spi:address5": "GL", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:14:32+00:00", "spi:address1": "<PERSON><PERSON><PERSON><PERSON>en 31-39, - Car<PERSON> 1, <PERSON> 58-60", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDUvVVNBRg--"}, {"_rowstamp": "115581750", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/5-24", "spi:addressid": 24, "spi:description": "FREDERCIA", "spi:addresscode": "30006", "spi:address5": "DK", "spi:address4": "7000", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:16:01+00:00", "spi:address2": "Fredericia", "spi:address1": "Greenland Warehouse c/oShipping.dk,Oceankajen 12", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDYvVVNBRg--"}, {"_rowstamp": "286515390", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/6-25", "spi:addressid": 25, "spi:description": "KASTRUP", "spi:addresscode": "30007", "spi:address5": "DK", "spi:address4": "2770", "spi:changeby": "PALA383021", "spi:changedate": "2022-05-05T13:28:21+00:00", "spi:address2": "Kastrup", "spi:address1": "Vectrus Services A/S, Worldwide Flight Services", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDcvVVNBRg--"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/7-26", "spi:addressid": 26, "spi:description": "MCGUIRE", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDgvVVNBRg--", "_rowstamp": "207611220", "spi:addresscode": "30008", "spi:address5": "US", "spi:address4": "08641", "spi:address3": "NJ", "spi:changedate": "2022-02-10T03:24:17+00:00", "spi:address2": "Joint Base MDL", "spi:address1": "Vectrus Services A/S 1757 Vandenberg Avenue"}, {"_rowstamp": "951841678", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/8-45", "spi:addressid": 45, "spi:description": "Vectrus Services A/S", "spi:addresscode": "30009", "spi:address5": "Denmark", "spi:changeby": "MOOR382170", "spi:address3": "<EMAIL>", "spi:changedate": "2023-10-30T18:00:54+00:00", "spi:address2": "2900 <PERSON><PERSON><PERSON>", "spi:address1": "Strandvejen 125", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMzAwMDkvVVNBRg--"}, {"_rowstamp": "115582653", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/9-27", "spi:addressid": 27, "spi:addresscode": "AIRSIDE ASSIST - DENMARK", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:19:36+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvQUlSU0lERSBBU1NJU1QgLSBERU5NQVJLL1VTQUY-"}, {"_rowstamp": "115582654", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/10-28", "spi:addressid": 28, "spi:addresscode": "COPENHAGEN OFFICE", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:19:47+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvQ09QRU5IQUdFTiBPRkZJQ0UvVVNBRg--"}, {"_rowstamp": "115582655", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/11-29", "spi:addressid": 29, "spi:addresscode": "MCGUIRE AFB - US", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:19:58+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvTUNHVUlSRSBBRkIgLSBVUy9VU0FG"}, {"_rowstamp": "115582656", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/12-30", "spi:addressid": 30, "spi:addresscode": "NORFOLK - US", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:20:06+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvTk9SRk9MSyAtIFVTL1VTQUY-"}, {"_rowstamp": "115582657", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/13-31", "spi:addressid": 31, "spi:addresscode": "PORT FEDERICA- DENMARK", "spi:changeby": "KRISH113979", "spi:changedate": "2021-09-27T03:20:16+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvUE9SVCBGRURFUklDQS0gREVOTUFSSy9VU0FG"}, {"_rowstamp": "115582658", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--/address/14-32", "spi:addressid": 32, "spi:description": "Vectrus Services A/S", "spi:addresscode": "<EMAIL>", "spi:address5": "Denmark", "spi:changeby": "KRISH113979", "spi:address3": "København", "spi:changedate": "2021-09-27T03:20:51+00:00", "spi:address2": "2100 København Ø", "spi:address1": "Indiavej 1,3", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvVkVDVFJVU0lOVk9JQ0VTQFZFQ1RSVVMuQ09NL1VTQUY-"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBRg--", "spi:enterdate": "2021-09-27T02:05:08+00:00", "spi:enterby": "DATASPLICE", "spi:plusgaddassetspec": true, "_rowstamp": "*********", "spi:category": "STK", "spi:clearingacct": "0000.0000.00000", "spi:companysetid": "COMPSET2", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 8, "spi:basecurrency1": "USD"}, {"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/0-1", "spi:siteuid": 1, "spi:description": "Qatar Base Operations Support & Service Bridge.", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvUUJPU1M-", "spi:enterdate": "2020-05-22T12:53:52+00:00", "spi:enterby": "PRABHAK", "_rowstamp": "*********", "spi:active": true, "spi:contact": "SOTO111313", "spi:changedate": "2022-11-07T10:13:39+00:00", "spi:plusgopenomuid": 1, "spi:systemid": "QATARLOC", "spi:siteid": "QBOSS", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "28530854", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/0-1/billtoshipto/0-1", "spi:shiptocontact": "SOTO111313", "spi:addresscode": "QBOSS", "spi:billtocontact": "SOTO111313", "spi:billtoshiptoid": 1, "spi:billto": true, "spi:shipto": true, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1FCT1NTL1VTQVJNWS9RQk9TUw--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10", "spi:siteuid": 10, "spi:description": "Logcap Thailand", "spi:changeby": "KUBE920000027", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTENWVA--", "spi:enterdate": "2021-01-21T09:10:02+00:00", "spi:enterby": "SHIV125743", "_rowstamp": "474859771", "spi:active": true, "spi:contact": "NORAMINA.ALONTO", "spi:changedate": "2022-11-07T10:13:42+00:00", "spi:plusgopenomuid": 10, "spi:systemid": "THAILANDLOC", "spi:siteid": "LCVT", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "470137149", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/0-55", "spi:shiptocontact": "MICHAEL.FIGUEROA", "spi:addresscode": "11095", "spi:billtoshiptoid": 55, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk1L1VTQVJNWS9MQ1ZU"}, {"spi:shiptodefault": true, "_rowstamp": "470137150", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/1-56", "spi:shiptocontact": "MICHAEL.FIGUEROA", "spi:addresscode": "24001", "spi:billtoshiptoid": 56, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzI0MDAxL1VTQVJNWS9MQ1ZU"}, {"spi:shiptodefault": false, "_rowstamp": "470137151", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/2-57", "spi:shiptocontact": "MICHAEL.FIGUEROA", "spi:addresscode": "24002", "spi:billtoshiptoid": 57, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzI0MDAyL1VTQVJNWS9MQ1ZU"}, {"spi:shiptodefault": false, "_rowstamp": "470137152", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/3-58", "spi:shiptocontact": "MICHAEL.FIGUEROA", "spi:addresscode": "24099", "spi:billtoshiptoid": 58, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzI0MDk5L1VTQVJNWS9MQ1ZU"}, {"spi:shiptodefault": false, "_rowstamp": "470136977", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/4-16", "spi:addresscode": "LCVT", "spi:billtoshiptoid": 16, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL0xDVlQvVVNBUk1ZL0xDVlQ-"}, {"spi:shiptodefault": false, "_rowstamp": "532352894", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/1-10/billtoshipto/5-64", "spi:shiptocontact": "NORAMINA.ALONTO", "spi:addresscode": "LCVT APO", "spi:billtoshiptoid": 64, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL0xDVlQgQVBPL1VTQVJNWS9MQ1ZU"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/2-11", "spi:siteuid": 11, "spi:description": "Logcap Iraq", "spi:changeby": "KUBE920000027", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTENWSVJR", "spi:enterdate": "2021-04-15T14:36:47+00:00", "spi:enterby": "SHIV125743", "_rowstamp": "474859772", "spi:active": true, "spi:contact": "HALL369408", "spi:changedate": "2022-11-07T10:13:44+00:00", "spi:plusgopenomuid": 11, "spi:systemid": "IRAQLOC", "spi:siteid": "LCVIRQ", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "256556390", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/2-11/billtoshipto/0-42", "spi:addresscode": "11039", "spi:billtoshiptoid": 42, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDM5L1VTQVJNWS9MQ1ZJUlE-"}, {"spi:shiptodefault": false, "_rowstamp": "260162722", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/2-11/billtoshipto/1-44", "spi:addresscode": "11084", "spi:billtoshiptoid": 44, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDg0L1VTQVJNWS9MQ1ZJUlE-"}, {"spi:shiptodefault": false, "_rowstamp": "256557279", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/2-11/billtoshipto/2-43", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 43, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNBUk1ZL0xDVklSUQ--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14", "spi:siteuid": 14, "spi:description": "Logcap Kuwait", "spi:changeby": "KUBE920000027", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTENWS1dU", "spi:enterdate": "2021-09-08T16:45:01+00:00", "spi:enterby": "KANJ125715", "_rowstamp": "474859773", "spi:active": true, "spi:contact": "SOFG118757", "spi:changedate": "2022-11-07T10:13:47+00:00", "spi:plusgopenomuid": 13, "spi:systemid": "KUWAITLOC", "spi:siteid": "LCVKWT", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "98392800", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/0-24", "spi:addresscode": "11013", "spi:billtoshiptoid": 24, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEzL1VTQVJNWS9MQ1ZLV1Q-"}, {"spi:shiptodefault": false, "_rowstamp": "98392801", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/1-25", "spi:addresscode": "11054", "spi:billtoshiptoid": 25, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDU0L1VTQVJNWS9MQ1ZLV1Q-"}, {"spi:shiptodefault": true, "_rowstamp": "98392802", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/2-26", "spi:addresscode": "11073", "spi:billtoshiptoid": 26, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDczL1VTQVJNWS9MQ1ZLV1Q-"}, {"spi:shiptodefault": false, "_rowstamp": "1525703436", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/3-41", "spi:shiptocontact": "DAVI369173", "spi:addresscode": "11098", "spi:billtoshiptoid": 41, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk4L1VTQVJNWS9MQ1ZLV1Q-"}, {"spi:shiptodefault": false, "_rowstamp": "98392799", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/3-14/billtoshipto/4-23", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 23, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNBUk1ZL0xDVktXVA--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/4-25", "spi:siteuid": 25, "spi:description": "Kuwait DFAC", "spi:changeby": "KRISH113979", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvS0RGQUM-", "spi:enterdate": "2022-02-11T06:08:41+00:00", "spi:enterby": "PALA383021", "_rowstamp": "765283371", "spi:active": true, "spi:changedate": "2023-06-29T16:01:25+00:00", "spi:plusgopenomuid": 23, "spi:systemid": "KDFACKUWAIT", "spi:siteid": "KDFAC", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "208610704", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/4-25/billtoshipto/0-38", "spi:addresscode": "11073", "spi:billtoshiptoid": 38, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDczL1VTQVJNWS9LREZBQw--"}, {"spi:shiptodefault": false, "_rowstamp": "208610705", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/4-25/billtoshipto/1-39", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 39, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNBUk1ZL0tERkFD"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/5-26", "spi:siteuid": 26, "spi:description": "Kwajalein Range", "spi:changeby": "KUBE920000027", "spi:vecfreight": "15", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvSUtXQUo-", "spi:enterdate": "2022-05-07T02:33:47+00:00", "spi:enterby": "KRISH113979", "_rowstamp": "474859775", "spi:active": true, "spi:contact": "BILL.COOLER", "spi:changedate": "2022-11-07T10:14:03+00:00", "spi:plusgopenomuid": 24, "spi:systemid": "KWAJLOC", "spi:siteid": "IKWAJ", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "289616590", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/5-26/billtoshipto/0-50", "spi:addresscode": "110100", "spi:billtoshiptoid": 50, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEwMC9VU0FSTVkvSUtXQUo-"}, {"spi:shiptodefault": true, "_rowstamp": "1145752602", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/5-26/billtoshipto/1-45", "spi:shiptocontact": "DONA384628", "spi:addresscode": "11089", "spi:billtoshiptoid": 45, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDg5L1VTQVJNWS9JS1dBSg--"}, {"spi:shiptodefault": false, "_rowstamp": "289616253", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/5-26/billtoshipto/2-49", "spi:addresscode": "11099", "spi:billtoshiptoid": 49, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk5L1VTQVJNWS9JS1dBSg--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28", "spi:siteuid": 28, "spi:description": "LOGCAPV Philippines", "spi:changeby": "KUBE920000027", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTEdDUEg-", "spi:enterdate": "2022-08-03T08:15:13+00:00", "spi:enterby": "PALA383021", "_rowstamp": "474859776", "spi:active": true, "spi:contact": "MAXW70947", "spi:changedate": "2022-11-07T10:14:07+00:00", "spi:plusgopenomuid": 25, "spi:systemid": "PHILLOC", "spi:siteid": "LGCPH", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "2310559710", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28/billtoshipto/0-86", "spi:addresscode": "110134", "spi:billtoshiptoid": 86, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEzNC9VU0FSTVkvTEdDUEg-"}, {"spi:shiptodefault": false, "_rowstamp": "2310559711", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28/billtoshipto/1-52", "spi:addresscode": "23101", "spi:billtoshiptoid": 52, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIzMTAxL1VTQVJNWS9MR0NQSA--"}, {"spi:shiptodefault": false, "_rowstamp": "*********", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28/billtoshipto/2-53", "spi:addresscode": "23102", "spi:billtoshiptoid": 53, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIzMTAyL1VTQVJNWS9MR0NQSA--"}, {"spi:shiptodefault": false, "_rowstamp": "*********", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/6-28/billtoshipto/3-51", "spi:addresscode": "23199", "spi:billtoshiptoid": 51, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIzMTk5L1VTQVJNWS9MR0NQSA--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30", "spi:siteuid": 30, "spi:description": "PMO Site", "spi:changeby": "PALA383021", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTEdDQVA-", "spi:enterdate": "2022-12-14T14:16:44+00:00", "spi:enterby": "PALA383021", "_rowstamp": "518134733", "spi:active": true, "spi:contact": "BILL.COOLER", "spi:changedate": "2022-12-15T08:15:12+00:00", "spi:plusgopenomuid": 27, "spi:siteid": "LGCAP", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "1959066539", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30/billtoshipto/0-59", "spi:addresscode": "11001", "spi:billtoshiptoid": 59, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDAxL1VTQVJNWS9MR0NBUA--"}, {"spi:shiptodefault": true, "_rowstamp": "1959066540", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30/billtoshipto/1-77", "spi:addresscode": "11013", "spi:billtoshiptoid": 77, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEzL1VTQVJNWS9MR0NBUA--"}, {"spi:shiptodefault": false, "_rowstamp": "518481100", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30/billtoshipto/2-60", "spi:addresscode": "11073", "spi:billtoshiptoid": 60, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDczL1VTQVJNWS9MR0NBUA--"}, {"spi:shiptodefault": false, "_rowstamp": "518481101", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/7-30/billtoshipto/3-61", "spi:addresscode": "11099", "spi:billtoshiptoid": 61, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk5L1VTQVJNWS9MR0NBUA--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/8-33", "spi:siteuid": 33, "spi:description": "LOGCAP Jordan", "spi:changeby": "KRISH113979", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQ0pPUkQ-", "spi:enterdate": "2024-07-12T14:55:02+00:00", "spi:enterby": "KRISH113979", "_rowstamp": "1622341852", "spi:active": true, "spi:changedate": "2024-07-12T14:55:51+00:00", "spi:plusgopenomuid": 30, "spi:systemid": "JORDANLOC", "spi:siteid": "CJORD", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "1861821384", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/8-33/billtoshipto/0-74", "spi:addresscode": "110125", "spi:billtoshiptoid": 74, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEyNS9VU0FSTVkvQ0pPUkQ-"}, {"spi:shiptodefault": false, "_rowstamp": "1809182203", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/site/8-33/billtoshipto/1-72", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 72, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNBUk1ZL0NKT1JE"}]}], "spi:itemsetid": "ITEMSET", "spi:description": "United States Army", "spi:dfltitemstatus_description": "Active", "spi:dfltitemstatus": "ACTIVE", "spi:orgid": "USARMY", "spi:address": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/0-50", "spi:addressid": 50, "spi:description": "UNITED STATES", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMDEvVVNBUk1Z", "_rowstamp": "517282121", "spi:addresscode": "11001", "spi:address5": "US - UNITED STATES", "spi:address4": "80919", "spi:address3": "Colorado Springs, CO", "spi:changedate": "2022-12-14T14:18:29+00:00", "spi:address2": "Suite 300", "spi:address1": "2424 Garden of the Gods Road"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/1-39", "spi:addressid": 39, "spi:description": "Vectrus Systems", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTAwL1VTQVJNWQ--", "_rowstamp": "1145873275", "spi:addresscode": "110100", "spi:address5": "US - UNITED STATES", "spi:address4": "96853", "spi:address3": "HI", "spi:changedate": "2024-02-12T15:30:06+00:00", "spi:address2": "Honolulu", "spi:address1": "Building 2116 Engine Test Road Hickam AFB"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/2-60", "spi:addressid": 60, "spi:description": "ARMY CONTRACTING COMMAND - ROCK ISL", "spi:changeby": "KRISH113979", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTEzL1VTQVJNWQ--", "_rowstamp": "1706523608", "spi:addresscode": "110113", "spi:address5": "US", "spi:address4": "61299", "spi:address3": "IL", "spi:changedate": "2024-07-17T06:47:51+00:00", "spi:address2": "Rock Island", "spi:address1": "3055 RODMAN AVE"}, {"_rowstamp": "1861820634", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/3-62", "spi:addressid": 62, "spi:description": "V2X Operations", "spi:addresscode": "110125", "spi:address5": "JO - Jordan", "spi:address4": "09315", "spi:changeby": "MOOR382170", "spi:address3": "APO, AE", "spi:changedate": "2024-08-02T18:17:31+00:00", "spi:address1": "KFAB, Jordan", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTI1L1VTQVJNWQ--"}, {"_rowstamp": "98392304", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/4-17", "spi:addressid": 17, "spi:description": "Central Material Warehouse - <PERSON>fjan", "spi:addresscode": "11013", "spi:address5": "Kuwait", "spi:changeby": "KANJ125715", "spi:address3": "<PERSON><PERSON><PERSON>", "spi:changedate": "2021-09-08T16:47:43+00:00", "spi:address2": "Camp <PERSON>jan", "spi:address1": "PWD T-3, ZONE-6", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTMvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/5-70", "spi:addressid": 70, "spi:description": "Samandra Office - Philippines", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTM0L1VTQVJNWQ--", "_rowstamp": "2310557083", "spi:addresscode": "110134", "spi:address5": "Philippines", "spi:address4": "80949", "spi:address3": "Brgy. Cawag, Subic Bay Freeport Zone 2222", "spi:changedate": "2025-03-03T14:42:56+00:00", "spi:address2": "Redondo Peninsula, Sitio, Agusuhin", "spi:address1": "Greenbeach1"}, {"_rowstamp": "258295275", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/6-36", "spi:addressid": 36, "spi:description": "Iraq Ship To Al Asad", "spi:addresscode": "11039", "spi:address5": "Iraq", "spi:address4": "09333 - APO", "spi:changeby": "SHIV125743", "spi:changedate": "2022-04-06T05:55:14+00:00", "spi:address2": "Camp Al Asad, IRAQ", "spi:address1": "Vectrus Materials", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMzkvVVNBUk1Z"}, {"_rowstamp": "98392305", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/7-18", "spi:addressid": 18, "spi:description": "Vectrus Systems Corporation/KBOSSS", "spi:addresscode": "11054", "spi:address5": "Kuwait", "spi:changeby": "KANJ125715", "spi:address3": "<PERSON><PERSON><PERSON>", "spi:changedate": "2021-09-08T16:48:10+00:00", "spi:address2": "Camp <PERSON>jan", "spi:address1": "PMT/RIP, APO, AE  09366", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNTQvVVNBUk1Z"}, {"_rowstamp": "331751951", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/8-19", "spi:addressid": 19, "spi:description": "Vectrus Systems Corporation", "spi:addresscode": "11073", "spi:address5": "Kuwait", "spi:changeby": "SHIV125743", "spi:address3": "Camp <PERSON>jan", "spi:changedate": "2022-06-23T14:06:40+00:00", "spi:address2": "Camp <PERSON>jan", "spi:address1": "U.S. MILITARY CRSP, BLK Tent, Lot 80, Zone2", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNzMvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/9-37", "spi:addressid": 37, "spi:description": "Iraq Ship To - Erbil", "spi:changeby": "SHIV125743", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwODQvVVNBUk1Z", "_rowstamp": "258296238", "spi:addresscode": "11084", "spi:address5": "IRAQ", "spi:address4": "09316 - APO", "spi:address3": "AE", "spi:changedate": "2022-04-06T05:56:11+00:00", "spi:address2": "Camp Erbil Air Base, IRAQ", "spi:address1": "Vectrus Materials"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/10-40", "spi:addressid": 40, "spi:description": "Oakland Cross-Dock", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwODkvVVNBUk1Z", "_rowstamp": "1145751690", "spi:addresscode": "11089", "spi:address5": "US - UNITED STATES", "spi:address4": "94603", "spi:address3": "CA", "spi:changedate": "2024-02-12T14:03:15+00:00", "spi:address2": "Oakland", "spi:address1": "9757 San Leandro St"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/11-46", "spi:addressid": 46, "spi:description": "ARMY CONTRACTING COMMAND", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwOTUvVVNBUk1Z", "_rowstamp": "470133418", "spi:addresscode": "11095", "spi:address5": "US - UNITED STATES", "spi:address4": "61299", "spi:address3": "IL", "spi:changedate": "2022-11-02T15:30:46+00:00", "spi:address2": "ROCK ISLAND", "spi:address1": "BLDGS 60 & 62"}, {"_rowstamp": "1525700155", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/12-35", "spi:addressid": 35, "spi:description": "Qatar", "spi:addresscode": "11098", "spi:changeby": "MOOR382170", "spi:address3": "Qatar", "spi:changedate": "2024-07-01T13:55:15+00:00", "spi:address2": "C.A.R.<PERSON>", "spi:address1": "Bldg 110 - Materials Warehouse Receiving Section", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwOTgvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/13-41", "spi:addressid": 41, "spi:description": "<EMAIL>", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwOTkvVVNBUk1Z", "_rowstamp": "**********", "spi:addresscode": "11099", "spi:address5": "US", "spi:address4": "Accounts Payable Box 4 PO Box 39710", "spi:address3": "OR Vectrus Systems Corporation", "spi:changedate": "2025-01-10T20:25:50+00:00", "spi:address2": "<EMAIL>", "spi:address1": "<EMAIL>"}, {"_rowstamp": "*********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/14-43", "spi:addressid": 43, "spi:description": "Philippines", "spi:addresscode": "23101", "spi:address5": "Philippines", "spi:changeby": "PALA383021", "spi:changedate": "2022-08-03T08:17:40+00:00", "spi:address2": "Manila", "spi:address1": "American/U.S. Embassy", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjMxMDEvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/15-44", "spi:addressid": 44, "spi:description": "Samandra Office - Philippines", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjMxMDIvVVNBUk1Z", "_rowstamp": "*********", "spi:addresscode": "23102", "spi:address5": "Philippines", "spi:address4": "80949", "spi:address3": "Brgy. Cawag, Subic Bay Freeport Zone 2222", "spi:changedate": "2022-08-03T08:19:31+00:00", "spi:address2": "Redondo Peninsula, Sitio, Agusuhin", "spi:address1": "Greenbeach1"}, {"_rowstamp": "*********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/16-42", "spi:addressid": 42, "spi:description": "Accounts Payable Box 4", "spi:addresscode": "23199", "spi:address5": "US - United States", "spi:changeby": "PALA383021", "spi:address3": "CO", "spi:changedate": "2022-08-03T08:16:53+00:00", "spi:address2": "80949 - Colorado Springs", "spi:address1": "PO Box 39710", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjMxOTkvVVNBUk1Z"}, {"_rowstamp": "*********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/17-47", "spi:addressid": 47, "spi:description": "454Garden City Soi", "spi:addresscode": "24001", "spi:address5": "TH - THAILAND", "spi:changeby": "PALA383021", "spi:address3": "Phrakanong Nua, Wattana", "spi:changedate": "2022-11-02T15:31:41+00:00", "spi:address2": "Ni<PERSON><PERSON>", "spi:address1": "<PERSON><PERSON><PERSON><PERSON><PERSON> 79", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjQwMDEvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/18-48", "spi:addressid": 48, "spi:description": "Army Aviation Centre 303", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjQwMDIvVVNBUk1Z", "_rowstamp": "470134799", "spi:addresscode": "24002", "spi:address5": "TH - THAILAND", "spi:address4": "Lopburi", "spi:address3": "15160 - <PERSON><PERSON>", "spi:changedate": "2022-11-02T15:32:43+00:00", "spi:address2": "Village #7, Sub-district: Khao P, 15160 - Muang", "spi:address1": "Hangar 277"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/19-49", "spi:addressid": 49, "spi:description": "PO Box 39710", "spi:changeby": "PALA383021", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjQwOTkvVVNBUk1Z", "_rowstamp": "*********", "spi:addresscode": "24099", "spi:address5": "US - United States", "spi:address4": "80949", "spi:address3": "CO", "spi:changedate": "2022-11-02T15:33:39+00:00", "spi:address2": "Colorado Springs", "spi:address1": "Accounts Payable Box 4"}, {"_rowstamp": "********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/20-10", "spi:addressid": 10, "spi:description": "Erbil Air Base", "spi:addresscode": "EAB", "spi:address5": "Iraq", "spi:changeby": "SHIV125743", "spi:changedate": "2021-04-15T14:36:32+00:00", "spi:address1": "CRSP Yard, Attn: Vectrus / LOGCAP, Erbil Air Base", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvRUFCL1VTQVJNWQ--"}, {"_rowstamp": "*********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/21-38", "spi:addressid": 38, "spi:description": "APO <PERSON>", "spi:addresscode": "KWAJ", "spi:changeby": "KRISH113979", "spi:changedate": "2022-05-07T02:35:56+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvS1dBSi9VU0FSTVk-"}, {"_rowstamp": "********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/22-9", "spi:addressid": 9, "spi:description": "Thailand Default Bill to Address", "spi:addresscode": "LCVT", "spi:address5": "Thailand", "spi:changeby": "SHIV125743", "spi:changedate": "2021-01-21T09:09:53+00:00", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvTENWVC9VU0FSTVk-"}, {"_rowstamp": "532352469", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/23-55", "spi:addressid": 55, "spi:description": "Vectrus/Supply", "spi:addresscode": "LCVT APO", "spi:address4": "96502", "spi:changeby": "MOOR382170", "spi:address3": "AP", "spi:changedate": "2022-12-28T19:50:46+00:00", "spi:address2": "APO", "spi:address1": "Unit 45201", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvTENWVCBBUE8vVVNBUk1Z"}, {"_rowstamp": "2532097", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/24-1", "spi:addressid": 1, "spi:description": "Qatar Base Opeartions, Support and Service", "spi:addresscode": "QBOSS", "spi:address5": "Qatar", "spi:changeby": "PRABHAK", "spi:address3": "Qatar", "spi:changedate": "2020-05-22T12:53:41+00:00", "spi:address2": "Doha", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvUUJPU1MvVVNBUk1Z"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z/address/25-16", "spi:addressid": 16, "spi:description": "Vectrus Systems", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvVkVDVFJVUy9VU0FSTVk-", "_rowstamp": "**********", "spi:addresscode": "VECTRUS", "spi:address5": "Colorado Springs, CO 80949 USA", "spi:address4": "Accounts Payable Box 4 PO Box 39710", "spi:address3": "OR Vectrus Systems Corporation", "spi:changedate": "2025-01-10T20:26:33+00:00", "spi:address2": "<EMAIL>", "spi:address1": "<EMAIL>"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNBUk1Z", "spi:enterdate": "2020-05-22T09:20:35+00:00", "spi:enterby": "KRISHNAMURTHYP", "spi:plusgaddassetspec": true, "_rowstamp": "********", "spi:category": "STK", "spi:clearingacct": "000000.0000.00000", "spi:companysetid": "COMPSET", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 1, "spi:basecurrency1": "USD"}, {"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3", "spi:siteuid": 3, "spi:description": "Romania Base Operations Support & Service Bridge", "spi:changeby": "KUBE920000027", "spi:vecfreight": "10", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvUkJPU1M-", "spi:enterdate": "2020-07-27T16:00:34+00:00", "spi:enterby": "AKANJILAL", "_rowstamp": "*********", "spi:active": true, "spi:contact": "SING364563", "spi:changedate": "2022-11-07T10:14:29+00:00", "spi:plusgopenomuid": 3, "spi:systemid": "ROMLOC1", "spi:siteid": "RBOSS", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "1959078227", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/0-62", "spi:addresscode": "11001", "spi:billtoshiptoid": 62, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDAxL1VTTkFWWS9SQk9TUw--"}, {"spi:shiptodefault": false, "_rowstamp": "644166180", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/1-67", "spi:addresscode": "110107", "spi:billtoshiptoid": 67, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEwNy9VU05BVlkvUkJPU1M-"}, {"spi:shiptodefault": true, "_rowstamp": "615476875", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/2-63", "spi:addresscode": "11065", "spi:billtoshiptoid": 63, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDY1L1VTTkFWWS9SQk9TUw--"}, {"spi:shiptodefault": false, "_rowstamp": "643995556", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/3-10", "spi:addresscode": "DEFAULT", "spi:billtoshiptoid": 10, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL0RFRkFVTFQvVVNOQVZZL1JCT1NT"}, {"spi:shiptodefault": false, "_rowstamp": "643995555", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/4-3", "spi:shiptocontact": "LAND355149", "spi:addresscode": "RBOSS", "spi:billtocontact": "LAND355149", "spi:billtoshiptoid": 3, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1JCT1NTL1VTTkFWWS9SQk9TUw--"}, {"spi:shiptodefault": false, "_rowstamp": "643995557", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/0-3/billtoshipto/5-11", "spi:addresscode": "VECTRUS", "spi:billtoshiptoid": 11, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1ZFQ1RSVVMvVVNOQVZZL1JCT1NT"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/1-7", "spi:siteuid": 7, "spi:description": "United States Army (GCSMAC)", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvVVNBUg--", "spi:enterdate": "2020-10-23T10:19:52+00:00", "spi:enterby": "KANJ125715", "_rowstamp": "474860355", "spi:active": true, "spi:contact": "PETE74683", "spi:changedate": "2022-11-07T10:14:32+00:00", "spi:plusgopenomuid": 7, "spi:systemid": "SHAFLOC", "spi:siteid": "USAR", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "28530209", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/1-7/billtoshipto/0-13", "spi:shiptocontact": "PETE74683", "spi:addresscode": "USAR", "spi:billtocontact": "PETE74683", "spi:billtoshiptoid": 13, "spi:billto": true, "spi:shipto": true, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL1VTQVIvVVNOQVZZL1VTQVI-"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/2-9", "spi:siteuid": 9, "spi:description": "ISA Bahrain", "spi:changeby": "KUBE920000027", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQkJPUw--", "spi:enterdate": "2020-11-27T09:04:04+00:00", "spi:enterby": "SHIV125743", "_rowstamp": "474860356", "spi:active": true, "spi:contact": "SING364563", "spi:changedate": "2022-11-07T10:14:34+00:00", "spi:plusgopenomuid": 9, "spi:systemid": "BBOSLOC", "spi:siteid": "BBOS", "spi:billtoshipto": [{"spi:shiptodefault": true, "_rowstamp": "55628968", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/2-9/billtoshipto/0-20", "spi:addresscode": "11043", "spi:billtoshiptoid": 20, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDQzL1VTTkFWWS9CQk9T"}, {"spi:shiptodefault": false, "_rowstamp": "55628969", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/2-9/billtoshipto/1-21", "spi:addresscode": "11097", "spi:billtoshiptoid": 21, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDk3L1VTTkFWWS9CQk9T"}, {"spi:shiptodefault": false, "_rowstamp": "55643177", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/2-9/billtoshipto/2-15", "spi:addresscode": "BBOS", "spi:billtoshiptoid": 15, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPL0JCT1MvVVNOQVZZL0JCT1M-"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/3-13", "spi:siteuid": 13, "spi:description": "Naval Station Guantanamo bay", "spi:changeby": "MOOR382170", "spi:vecfreight": "20", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvTlNHQkE-", "spi:enterdate": "2021-07-07T14:42:44+00:00", "spi:enterby": "SHIV125743", "_rowstamp": "1973957508", "spi:active": true, "spi:contact": "TOLE358169", "spi:changedate": "2024-10-01T12:17:58+00:00", "spi:plusgopenomuid": 12, "spi:systemid": "NSGBALOC", "spi:siteid": "NSGBA", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "1887465229", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/3-13/billtoshipto/0-75", "spi:addresscode": "110126", "spi:billtoshiptoid": 75, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEyNi9VU05BVlkvTlNHQkE-"}, {"spi:shiptodefault": true, "_rowstamp": "2299135065", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/3-13/billtoshipto/1-78", "spi:shiptocontact": "ANGL3835206", "spi:addresscode": "110132", "spi:billtoshiptoid": 78, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDEzMi9VU05BVlkvTlNHQkE-"}, {"spi:shiptodefault": false, "_rowstamp": "2299135066", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/3-13/billtoshipto/2-22", "spi:addresscode": "11023", "spi:billtoshiptoid": 22, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDIzL1VTTkFWWS9OU0dCQQ--"}]}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/4-35", "spi:siteuid": 35, "spi:description": "Poland Base Operations Support & Service", "spi:changeby": "KRISH113979", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvUEJPUw--", "spi:enterdate": "2025-01-28T10:41:45+00:00", "spi:enterby": "KRISH113979", "_rowstamp": "2232929960", "spi:active": true, "spi:changedate": "2025-01-28T10:42:03+00:00", "spi:plusgopenomuid": 32, "spi:systemid": "PBOSLOC", "spi:siteid": "PBOS", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "2275320652", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/4-35/billtoshipto/0-84", "spi:addresscode": "11001", "spi:billtoshiptoid": 84, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzExMDAxL1VTTkFWWS9QQk9T"}, {"spi:shiptodefault": true, "_rowstamp": "2275320655", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/site/4-35/billtoshipto/1-85", "spi:addresscode": "20008", "spi:billtoshiptoid": 85, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMDA4L1VTTkFWWS9QQk9T"}]}], "spi:itemsetid": "ITEMSET1", "spi:description": "United States Navy", "spi:dfltitemstatus_description": "Active", "spi:dfltitemstatus": "ACTIVE", "spi:orgid": "USNAVY", "spi:address": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/0-53", "spi:addressid": 53, "spi:description": "UNITED STATES", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMDEvVVNOQVZZ", "_rowstamp": "1894146681", "spi:addresscode": "11001", "spi:address5": "US - UNITED STATES", "spi:address4": "80919", "spi:address3": "Colorado Springs, CO", "spi:changedate": "2024-08-20T20:31:36+00:00", "spi:address2": "2424 Garden of the Gods Road Suite 300", "spi:address1": "Vectrus Systems LLC"}, {"_rowstamp": "644165534", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/1-56", "spi:addressid": 56, "spi:description": "RBOSS FPO Shipping address", "spi:addresscode": "110107", "spi:address4": "09712", "spi:changeby": "MOOR382170", "spi:address3": "AE", "spi:changedate": "2023-03-28T18:04:45+00:00", "spi:address2": "FPO", "spi:address1": "PSC 825 BOX 212", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTA3L1VTTkFWWQ--"}, {"_rowstamp": "1887464857", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/2-63", "spi:addressid": 63, "spi:description": "IBC Airways c/o Vectrus", "spi:addresscode": "110126", "spi:address4": "33166", "spi:changeby": "MOOR382170", "spi:address3": "FL", "spi:changedate": "2024-08-16T16:49:16+00:00", "spi:address2": "Miami", "spi:address1": "IBC Airways Inc 5600 NW 36 Street", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTI2L1VTTkFWWQ--"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/3-65", "spi:addressid": 65, "spi:description": "1st Coast Cargo, Inc c/o V2X", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMTMyL1VTTkFWWQ--", "_rowstamp": "2033837343", "spi:addresscode": "110132", "spi:address5": "US", "spi:address4": "32218", "spi:address3": "FL", "spi:changedate": "2024-10-29T19:54:34+00:00", "spi:address2": "Jacksonville", "spi:address1": "1400 Eastport Road"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/4-15", "spi:addressid": 15, "spi:description": "Naval Station Guantanamo bay", "spi:changeby": "SHIV125743", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwMjMvVVNOQVZZ", "_rowstamp": "75260488", "spi:addresscode": "11023", "spi:address5": "USA", "spi:address4": "32254", "spi:address3": "FL", "spi:changedate": "2021-07-07T14:42:35+00:00", "spi:address2": "JACKSONVILLE", "spi:address1": "VECTRUS SYSTEMS CORPORATION PO# XXXXXX 6801 W 12th"}, {"_rowstamp": "55628705", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/5-13", "spi:addressid": 13, "spi:description": "U.S. Navy: Sheik ISA Air Base", "spi:addresscode": "11043", "spi:address4": "09859", "spi:changeby": "TOMY125770", "spi:address3": "AE", "spi:changedate": "2021-04-16T14:52:15+00:00", "spi:address2": "FPO", "spi:address1": "Vectrus Materials Building 8100", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNDMvVVNOQVZZ"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/6-54", "spi:addressid": 54, "spi:description": "US Navy Facility", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwNjUvVVNOQVZZ", "_rowstamp": "644163095", "spi:addresscode": "11065", "spi:address5": "Romania", "spi:address4": "237130", "spi:address3": "Judet Olt", "spi:changedate": "2023-03-28T18:01:43+00:00", "spi:address2": "<PERSON><PERSON><PERSON>", "spi:address1": "Unitatea Militara 01871, str. <PERSON><PERSON> 17"}, {"_rowstamp": "********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/7-14", "spi:addressid": 14, "spi:description": "Vectrus Systems Corporation", "spi:addresscode": "11097", "spi:address4": "80449", "spi:changeby": "TOMY125770", "spi:address3": "CO", "spi:changedate": "2021-04-16T14:52:51+00:00", "spi:address2": "Colorado Springs", "spi:address1": "PO Box 39710, Accounts Payable Box 4", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMTEwOTcvVVNOQVZZ"}, {"_rowstamp": "**********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/8-69", "spi:addressid": 69, "spi:description": "Naval Support Facility (NSF)", "spi:addresscode": "20008", "spi:changeby": "MOOR382170", "spi:address3": "PL - POLAND", "spi:changedate": "2025-04-22T12:22:12+00:00", "spi:address2": "Redzikowo", "spi:address1": "Naval Support Facility (NSF)", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAwMDgvVVNOQVZZ"}, {"_rowstamp": "********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/9-8", "spi:addressid": 8, "spi:description": "Bahrain Default Bill to Address", "spi:addresscode": "BBOS", "spi:address5": "BAHRAIN", "spi:address4": "09859", "spi:changeby": "SHIV125743", "spi:changedate": "2020-11-27T09:02:20+00:00", "spi:address2": "FPO, AE 09859", "spi:address1": "ISA BAHRAIN US NAVY, VECTRUS MATERIALS BAHRAIN", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvQkJPUy9VU05BVlk-"}, {"_rowstamp": "12447714", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/10-5", "spi:addressid": 5, "spi:description": "Default Shipping Address", "spi:addresscode": "DEFAULT", "spi:changeby": "JACOB.GEORGE", "spi:changedate": "2020-07-30T06:58:47+00:00", "spi:address2": "FPO AE 09712-0005", "spi:address1": "PSC 825 BOX 212", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvREVGQVVMVC9VU05BVlk-"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/11-3", "spi:addressid": 3, "spi:description": "Romania Base Operations Support & Service Bridge", "spi:changeby": "AKANJILAL", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvUkJPU1MvVVNOQVZZ", "_rowstamp": "11849000", "spi:addresscode": "RBOSS", "spi:address5": "Romania", "spi:address4": "235200", "spi:address3": "Olt County", "spi:changedate": "2020-07-27T16:00:18+00:00", "spi:address2": "<PERSON><PERSON><PERSON>", "spi:address1": "<PERSON><PERSON><PERSON><PERSON>, US NATO Facility UM 01871"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/12-7", "spi:addressid": 7, "spi:description": "USAR De<PERSON>ult Bill to Address", "spi:changeby": "KANJ125715", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvVVNBUi9VU05BVlk-", "_rowstamp": "24348459", "spi:addresscode": "USAR", "spi:address5": "USA", "spi:address4": "80919", "spi:address3": "Colorado", "spi:changedate": "2020-10-23T10:16:51+00:00", "spi:address2": "Colorado Springs", "spi:address1": "2424 West Garden of the Gods Rd, Suite 300 Bldg. E"}, {"_rowstamp": "12447740", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ/address/13-6", "spi:addressid": 6, "spi:description": "<PERSON><PERSON><PERSON><PERSON>", "spi:addresscode": "VECTRUS", "spi:address5": "Romania", "spi:changeby": "JACOB.GEORGE", "spi:address3": "Jud. Olt. 235200", "spi:changedate": "2020-07-30T06:58:58+00:00", "spi:address2": "<PERSON><PERSON><PERSON>", "spi:address1": "US NATO FACILITY UM 01871", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvVkVDVFJVUy9VU05BVlk-"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VVNOQVZZ", "spi:enterdate": "2020-07-27T15:59:15+00:00", "spi:enterby": "AKANJILAL", "spi:plusgaddassetspec": true, "_rowstamp": "54134892", "spi:category": "STK", "spi:clearingacct": "000000.0000.00000", "spi:companysetid": "COMPSET1", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 6, "spi:basecurrency1": "USD"}, {"spi:site": [{"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32", "spi:siteuid": 32, "spi:description": "DLA", "spi:changeby": "MOOR382170", "spi:vecfreight": "0", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvRExB", "spi:enterdate": "2024-04-25T13:59:38+00:00", "spi:enterby": "MOOR382170", "_rowstamp": "1273477577", "spi:active": true, "spi:changedate": "2024-04-25T14:01:19+00:00", "spi:plusgopenomuid": 29, "spi:siteid": "DLA", "spi:billtoshipto": [{"spi:shiptodefault": false, "_rowstamp": "1935002149", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/0-76", "spi:shiptocontact": "CHIL391671", "spi:addresscode": "20052", "spi:billtoshiptoid": 76, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMDUyL1ZTRS9ETEE-"}, {"spi:shiptodefault": true, "_rowstamp": "1353929377", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/1-69", "spi:addresscode": "20098", "spi:billtoshiptoid": 69, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMDk4L1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "1372034516", "spi:billtodefault": true, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/2-70", "spi:addresscode": "20099", "spi:billtocontact": "<EMAIL>", "spi:billtoshiptoid": 70, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMDk5L1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "2201760343", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/3-79", "spi:addresscode": "20102", "spi:billtoshiptoid": 79, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMTAyL1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "2223472564", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/4-80", "spi:addresscode": "20103", "spi:billtoshiptoid": 80, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMTAzL1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "2312912121", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/5-87", "spi:addresscode": "20104", "spi:billtoshiptoid": 87, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMTA0L1ZTRS9ETEE-"}, {"spi:shiptodefault": false, "_rowstamp": "2253030898", "spi:billtodefault": false, "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/site/0-32/billtoshipto/6-81", "spi:addresscode": "201104", "spi:billtoshiptoid": 81, "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL1NJVEUvQklMTFRPU0hJUFRPLzIwMTEwNC9WU0UvRExB"}]}], "spi:itemsetid": "ITEMSET3", "spi:description": "VSE", "spi:dfltitemstatus_description": "Active", "spi:dfltitemstatus": "ACTIVE", "spi:orgid": "VSE", "spi:address": [{"_rowstamp": "1936777684", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/0-64", "spi:addressid": 64, "spi:description": "<PERSON> Childers", "spi:addresscode": "20052", "spi:changeby": "MOOR382170", "spi:changedate": "2024-09-11T19:05:29+00:00", "spi:address2": "<EMAIL>", "spi:address1": "Contact Sierra Childers prior to shipping", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAwNTIvVlNF"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/1-58", "spi:addressid": 58, "spi:description": "V2X", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAwOTgvVlNF", "_rowstamp": "1372375898", "spi:addresscode": "20098", "spi:address5": "US", "spi:address4": "23323", "spi:address3": "VA", "spi:changedate": "2024-05-31T19:24:43+00:00", "spi:address2": "Chesapeake", "spi:address1": "713 Fenway Ave. Suite B"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/2-59", "spi:addressid": 59, "spi:description": "Vectrus Systems Corporation", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAwOTkvVlNF", "_rowstamp": "**********", "spi:addresscode": "20099", "spi:address5": "US", "spi:address4": "80949", "spi:address3": "CO", "spi:changedate": "2024-05-28T12:17:25+00:00", "spi:address2": "Colorado Springs", "spi:address1": "PO Box 39710 Accounts Payable Box 4"}, {"_rowstamp": "**********", "localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/3-66", "spi:addressid": 66, "spi:description": "Heale Manufacturing Co.", "spi:addresscode": "20102", "spi:address4": "53186", "spi:changeby": "MOOR382170", "spi:address3": "WI", "spi:changedate": "2025-01-14T19:54:13+00:00", "spi:address2": "<PERSON><PERSON><PERSON><PERSON>", "spi:address1": "1231 The Strand", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAxMDIvVlNF"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/4-67", "spi:addressid": 67, "spi:description": "Advance Design & Manufacturing Corp.", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAxMDMvVlNF", "_rowstamp": "**********", "spi:addresscode": "20103", "spi:address5": "US", "spi:address4": "22312", "spi:address3": "VA", "spi:changedate": "2025-01-24T11:43:05+00:00", "spi:address2": "Alenandria", "spi:address1": "6460 A General Green Way"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/5-71", "spi:addressid": 71, "spi:description": "Quality Support Attn:<PERSON>", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAxMDQvVlNF", "_rowstamp": "2312909367", "spi:addresscode": "20104", "spi:address5": "US", "spi:address4": "15683", "spi:address3": "PA", "spi:changedate": "2025-03-04T15:07:20+00:00", "spi:address2": "Scottdale", "spi:address1": "900 Water Street"}, {"localref": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF/address/6-68", "spi:addressid": 68, "spi:description": "Valence Surface Technologies/H&W Global Industries", "spi:changeby": "MOOR382170", "rdf:about": "http://childkey#T1JHQU5JWkFUSU9OL0FERFJFU1MvMjAxMTA0L1ZTRQ--", "_rowstamp": "2253029471", "spi:addresscode": "201104", "spi:address5": "US", "spi:address4": "15717", "spi:address3": "PA", "spi:changedate": "2025-02-05T19:01:32+00:00", "spi:address2": "Blairsville", "spi:address1": "414 Innovation Drive"}], "rdf:about": "https://vectrus-mea.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiorganization/_VlNF", "spi:enterdate": "2024-04-25T13:57:47+00:00", "spi:enterby": "MOOR382170", "spi:plusgaddassetspec": true, "_rowstamp": "1273476935", "spi:category": "STK", "spi:clearingacct": "000000.000000.000000", "spi:companysetid": "COMPSET3", "spi:plusgaddfailcode": true, "spi:active": true, "spi:organizationid": 10, "spi:basecurrency1": "USD"}]}