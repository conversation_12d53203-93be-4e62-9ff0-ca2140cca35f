<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold" fill="#0f3460">Lightning-Fast OAuth Authentication Flow</text>
  
  <!-- Actors -->
  <g id="actors">
    <!-- User -->
    <circle cx="100" cy="100" r="30" fill="#3498db"/>
    <text x="100" y="105" font-family="Arial" font-size="14" text-anchor="middle" fill="white" font-weight="bold">User</text>
    
    <!-- Browser -->
    <rect x="200" y="70" width="100" height="60" rx="5" ry="5" fill="#2ecc71"/>
    <text x="250" y="105" font-family="Arial" font-size="14" text-anchor="middle" fill="white" font-weight="bold">Browser</text>
    
    <!-- Flask App -->
    <rect x="350" y="70" width="100" height="60" rx="5" ry="5" fill="#e74c3c"/>
    <text x="400" y="105" font-family="Arial" font-size="14" text-anchor="middle" fill="white" font-weight="bold">Flask App</text>
    
    <!-- Token Manager -->
    <rect x="500" y="70" width="100" height="60" rx="5" ry="5" fill="#f39c12"/>
    <text x="550" y="105" font-family="Arial" font-size="14" text-anchor="middle" fill="white" font-weight="bold">Token Mgr</text>
    
    <!-- Maximo -->
    <rect x="650" y="70" width="100" height="60" rx="5" ry="5" fill="#9b59b6"/>
    <text x="700" y="105" font-family="Arial" font-size="14" text-anchor="middle" fill="white" font-weight="bold">Maximo</text>
  </g>
  
  <!-- Flow Lines -->
  <g id="flow-lines" stroke="#34495e" stroke-width="2">
    <!-- Vertical timeline lines -->
    <line x1="100" y1="130" x2="100" y2="550" stroke-dasharray="5,5"/>
    <line x1="250" y1="130" x2="250" y2="550" stroke-dasharray="5,5"/>
    <line x1="400" y1="130" x2="400" y2="550" stroke-dasharray="5,5"/>
    <line x1="550" y1="130" x2="550" y2="550" stroke-dasharray="5,5"/>
    <line x1="700" y1="130" x2="700" y2="550" stroke-dasharray="5,5"/>
  </g>
  
  <!-- Flow Steps -->
  <g id="flow-steps">
    <!-- Step 1: User enters credentials -->
    <line x1="100" y1="160" x2="250" y2="160" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="120" y="140" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="175" y="155" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Enter Credentials</text>
    
    <!-- Step 2: Browser submits form -->
    <line x1="250" y1="200" x2="400" y2="200" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="270" y="180" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="325" y="195" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Submit Login Form</text>
    
    <!-- Step 3: Flask starts background auth -->
    <line x1="400" y1="240" x2="550" y2="240" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="420" y="220" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="475" y="235" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Start Background Auth</text>
    
    <!-- Step 4: Flask returns loading page -->
    <line x1="400" y1="280" x2="250" y2="280" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="270" y="260" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="325" y="275" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Show Loading Page</text>
    
    <!-- Step 5: Browser shows loading page -->
    <line x1="250" y1="320" x2="100" y2="320" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="120" y="300" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="175" y="315" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">See Loading Progress</text>
    
    <!-- Step 6: Token Manager checks cache -->
    <line x1="550" y1="360" x2="550" y2="360" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="570" y="350" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="625" y="365" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Check Token Cache</text>
    
    <!-- Step 7: Token Manager authenticates with Maximo -->
    <line x1="550" y1="400" x2="700" y2="400" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="570" y="380" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="625" y="395" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Authenticate</text>
    
    <!-- Step 8: Maximo returns tokens -->
    <line x1="700" y1="440" x2="550" y2="440" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="570" y="420" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="625" y="435" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Return Tokens</text>
    
    <!-- Step 9: Token Manager caches tokens -->
    <line x1="550" y1="480" x2="550" y2="480" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="570" y="470" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="625" y="485" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Cache Tokens</text>
    
    <!-- Step 10: Token Manager notifies Flask -->
    <line x1="550" y1="520" x2="400" y2="520" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="420" y="500" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="475" y="515" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Auth Complete</text>
    
    <!-- Step 11: Flask redirects to welcome -->
    <line x1="400" y1="560" x2="250" y2="560" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
    <rect x="270" y="540" width="110" height="20" rx="5" ry="5" fill="#ecf0f1"/>
    <text x="325" y="555" font-family="Arial" font-size="10" text-anchor="middle" fill="#34495e">Redirect to Welcome</text>
  </g>
  
  <!-- Optimizations Callouts -->
  <g id="optimizations">
    <!-- Connection Pooling -->
    <rect x="20" y="380" width="150" height="40" rx="20" ry="20" fill="#3498db" opacity="0.8"/>
    <text x="95" y="405" font-family="Arial" font-size="12" text-anchor="middle" fill="white" font-weight="bold">Connection Pooling</text>
    <line x1="170" y1="400" x2="550" y2="400" stroke="#3498db" stroke-width="1" stroke-dasharray="3,3"/>
    
    <!-- URL Caching -->
    <rect x="20" y="440" width="150" height="40" rx="20" ry="20" fill="#2ecc71" opacity="0.8"/>
    <text x="95" y="465" font-family="Arial" font-size="12" text-anchor="middle" fill="white" font-weight="bold">Auth URL Caching</text>
    <line x1="170" y1="460" x2="550" y2="360" stroke="#2ecc71" stroke-width="1" stroke-dasharray="3,3"/>
    
    <!-- Token Caching -->
    <rect x="20" y="500" width="150" height="40" rx="20" ry="20" fill="#e74c3c" opacity="0.8"/>
    <text x="95" y="525" font-family="Arial" font-size="12" text-anchor="middle" fill="white" font-weight="bold">Token Caching</text>
    <line x1="170" y1="520" x2="550" y2="480" stroke="#e74c3c" stroke-width="1" stroke-dasharray="3,3"/>
  </g>
  
  <!-- Arrowhead marker -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- Footer -->
  <text x="400" y="590" font-family="Arial" font-size="12" text-anchor="middle" fill="#7f8c8d">Developed by Praba Krishna @2023</text>
</svg>
