# Editable URL Feature

## Overview
The login screen now includes an editable Maximo URL field that allows users to change the Maximo server URL on the fly. This feature enables connecting to different Maximo environments without modifying configuration files.

## Features

### 1. Editable URL Field
- **Location**: Login screen, above username/password fields
- **Default Value**: Loads from `MAXIMO_BASE_URL` in `.env` file
- **Validation**: Ensures URL starts with `http://` or `https://`
- **Auto-formatting**: Automatically adds `https://` if protocol is missing

### 2. Toggle Visibility
- **Show/Hide Toggle**: Switch to show or hide the URL field
- **Default State**: URL field is visible by default
- **Persistent**: When hidden, uses the current/default URL

### 3. Real-time Updates
- **Live Display**: Shows current URL being used at bottom of login form
- **Dynamic Updates**: URL display updates as you type
- **Session Storage**: URL is stored in user session

### 4. Persistent Storage
- **Auto-save to .env**: When URL changes, automatically updates `.env` file
- **Cross-session**: URL persists across application restarts
- **Backup**: Original URL is preserved if update fails

## Usage

### Basic Usage
1. Open the login page
2. The URL field shows the current Maximo URL
3. Edit the URL to point to a different Maximo environment
4. Enter username and password
5. Click "Lightning-Fast Login"

### Advanced Usage
1. **Hide URL Field**: Toggle "Show URL Field" switch to hide the URL input
2. **Quick Environment Switch**: Change URL to switch between:
   - Production: `https://prod.maximo.company.com/maximo`
   - UAT: `https://uat.maximo.company.com/maximo`
   - Development: `https://dev.maximo.company.com/maximo`

## Technical Details

### URL Validation
- Must start with `http://` or `https://`
- Auto-adds `https://` if protocol missing
- Validates format before submission
- Shows error message for invalid URLs

### Session Management
- URL stored in Flask session: `session['maximo_url']`
- Token manager recreated when URL changes
- All services re-initialized with new URL
- Cache cleared when switching environments

### Environment File Updates
- Updates `MAXIMO_BASE_URL` in `.env` file
- Preserves other environment variables
- Creates backup if file doesn't exist
- Logs success/failure of updates

### Service Re-initialization
When URL changes, the following are recreated:
- `MaximoTokenManager`
- `EnhancedProfileService`
- `EnhancedWorkOrderService`
- `LaborSearchService`
- `LaborRequestService`

## Error Handling

### Invalid URL Format
- Shows error message: "Maximo URL must start with http:// or https://"
- Prevents form submission
- Focuses URL field for correction

### Network Issues
- Token manager handles connection failures
- Appropriate error messages shown
- Fallback to previous working URL

### File System Issues
- Logs warning if `.env` file update fails
- Application continues with session URL
- Manual `.env` update may be required

## Configuration

### Default URL
Set in `.env` file:
```
MAXIMO_BASE_URL=https://your-maximo-server.com/maximo
```

### JavaScript Configuration
The URL field behavior can be customized by modifying the JavaScript in `templates/login.html`:
- Toggle default state
- Validation rules
- Auto-formatting behavior

## Security Considerations

### URL Validation
- Only allows HTTP/HTTPS protocols
- Prevents JavaScript injection
- Validates URL format before use

### Session Security
- URL stored in secure Flask session
- Session data encrypted with secret key
- No URL data in client-side storage

### Environment File
- `.env` file should not be committed to version control
- Contains sensitive configuration data
- Proper file permissions recommended

## Troubleshooting

### URL Field Not Showing
1. Check if "Show URL Field" toggle is enabled
2. Refresh the page
3. Clear browser cache

### URL Not Persisting
1. Check `.env` file permissions
2. Verify application has write access
3. Check application logs for errors

### Connection Issues
1. Verify URL is accessible
2. Check network connectivity
3. Validate Maximo server is running
4. Check firewall/proxy settings

## Future Enhancements

### Planned Features
- URL history/favorites
- Environment presets
- URL validation with ping test
- Import/export URL configurations
- Multi-tenant URL management

### API Integration
- REST endpoint to update URL programmatically
- Bulk URL configuration management
- Integration with configuration management systems
