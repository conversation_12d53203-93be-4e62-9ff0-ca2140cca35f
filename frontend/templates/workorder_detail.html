{% extends "base.html" %}

{% block title %}Work Order {{ workorder.wonum }} - Details{% endblock %}

{% block extra_css %}
<style>
    .workorder-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .back-button {
        margin-bottom: 20px;
    }

    .workorder-header {
        background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
    }

    .workorder-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 300;
    }

    .workorder-header .subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
        margin-top: 10px;
    }

    .status-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }

    /* Enhanced Tab Navigation */
    .nav-tabs-enhanced {
        border: none;
        background: #f8f9fa;
        border-radius: 12px;
        padding: 8px;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .nav-tabs-enhanced .nav-link {
        border: none;
        border-radius: 8px;
        padding: 1rem 1.5rem;
        margin: 0 4px;
        color: #6c757d;
        font-weight: 500;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        background: transparent;
    }

    .nav-tabs-enhanced .nav-link:hover {
        background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
        color: #495057;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .nav-tabs-enhanced .nav-link.active {
        background: linear-gradient(135deg, #fd7e14, #e55a00);
        color: white;
        box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
        transform: translateY(-1px);
    }

    .nav-tabs-enhanced .nav-link i {
        font-size: 1.2rem;
        margin-right: 0.75rem;
        transition: transform 0.3s ease;
    }

    .nav-tabs-enhanced .nav-link:hover i {
        transform: scale(1.1);
    }

    .nav-tabs-enhanced .nav-link.active i {
        transform: scale(1.15);
    }

    /* Enhanced Tab Content */
    .tab-content-enhanced {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .tab-pane-enhanced {
        padding: 2rem;
        min-height: 400px;
    }

    /* Enhanced Detail Rows */
    .detail-row-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid #f1f3f4;
        transition: all 0.3s ease;
    }

    .detail-row-enhanced:hover {
        background: #f8f9fa;
        padding-left: 1rem;
        padding-right: 1rem;
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 8px;
    }

    .detail-row-enhanced:last-child {
        border-bottom: none;
    }

    .detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        min-width: 200px;
        margin-right: 1rem;
    }

    .detail-label-enhanced i {
        font-size: 1.3rem;
        margin-right: 0.75rem;
        width: 20px;
        text-align: center;
    }

    .detail-value-enhanced {
        font-size: 1rem;
        color: #212529;
        font-weight: 500;
        flex: 1;
        text-align: right;
        word-wrap: break-word;
    }

    .detail-value-enhanced.empty {
        color: #6c757d;
        font-style: italic;
    }

    /* Sticky Task Header */
    .task-header-sticky {
        position: sticky;
        top: 0;
        z-index: 1020;
        background: linear-gradient(135deg, #3498db 0%, #17a2b8 50%, #20c997 100%);
        color: white;
        padding: 1rem;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
        margin-bottom: 0;
    }

    .task-header-sticky h5 {
        margin: 0;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .task-header-sticky .task-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-top: 0.5rem;
        align-items: center;
    }

    .task-header-sticky .badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .task-description-sticky {
        background: rgba(255, 255, 255, 0.1);
        padding: 0.75rem;
        border-radius: 6px;
        margin-top: 0.75rem;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Sticky Navigation */
    .task-navigation-sticky {
        position: sticky;
        bottom: 0;
        z-index: 1020;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-top: 1px solid #dee2e6;
        padding: 0.75rem;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    }

    .task-navigation-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 400px;
        margin: 0 auto;
    }

    .task-nav-btn {
        background: linear-gradient(135deg, #3498db, #17a2b8);
        color: white;
        border: none;
        padding: 0.6rem 1.2rem;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 80px;
        justify-content: center;
    }

    .task-nav-btn:hover:not(:disabled) {
        background: linear-gradient(135deg, #2980b9, #138496);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    }

    .task-nav-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .task-nav-info {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
    }

    /* Task Pagination - Updated for compatibility */
    .task-pagination {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 12px;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .task-card-enhanced {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .task-card-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #fd7e14;
    }

    .task-header-enhanced {
        background: linear-gradient(135deg, #2c3e50, #34495e);
        color: white;
        padding: 1.5rem;
        position: sticky;
        top: 65px;
        z-index: 100;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .task-header-enhanced::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3498db, #17a2b8, #20c997);
    }

    .task-body-enhanced {
        padding: 1.5rem;
    }

    .task-actions-enhanced {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .task-action-btn {
        flex: 1;
        min-width: 120px;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .task-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .pagination-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 1.5rem;
    }

    /* Desktop Pagination Buttons */
    .pagination-btn {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        min-width: auto;
        white-space: nowrap;
        height: auto;
        line-height: 1;
    }

    /* Mobile Pagination Buttons */
    @media (max-width: 767.98px) {
        .pagination-btn {
            padding: 0.1rem 0.2rem;
            border-radius: 2px;
            font-weight: 400;
            font-size: 0.55rem;
            gap: 0.05rem;
            height: 18px;
        }
    }

    .pagination-btn:hover:not(:disabled) {
        background: linear-gradient(135deg, #17a2b8, #138496);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
    }

    .pagination-btn:disabled {
        background: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
        opacity: 0.6;
    }

    /* Desktop Pagination Info */
    .pagination-info {
        background: #f8f9fa;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        color: #495057;
        font-weight: 500;
        font-size: 0.9rem;
        white-space: nowrap;
        height: auto;
        display: inline-flex;
        align-items: center;
        line-height: 1;
    }

    /* Mobile Pagination Info */
    @media (max-width: 767.98px) {
        .pagination-info {
            padding: 0.1rem 0.2rem;
            border-radius: 2px;
            font-weight: 400;
            font-size: 0.55rem;
            height: 18px;
        }
    }

    /* Desktop Navigation */
    .task-pagination {
        position: sticky;
        bottom: 0;
        background: white;
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        margin-top: 1rem;
        z-index: 10;
        text-align: center;
    }

    /* Mobile Responsive Adjustments */
    @media (max-width: 767.98px) {
        .task-header-sticky {
            padding: 0.75rem;
            border-radius: 0;
        }

        .task-header-sticky h5 {
            font-size: 1rem;
        }

        .task-header-sticky .task-meta {
            gap: 0.5rem;
        }

        .task-description-sticky {
            padding: 0.5rem;
            font-size: 0.85rem;
        }

        .task-navigation-sticky {
            padding: 0.5rem;
        }

        .task-nav-btn {
            padding: 0.5rem 0.8rem;
            font-size: 0.85rem;
            min-width: 70px;
        }

        .task-nav-info {
            font-size: 0.8rem;
        }

        /* Task Content Scrollable Area */
        .task-content-scrollable {
            height: calc(100vh - 200px);
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Task Tabs Mobile */
        .task-main-tabs .nav-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.85rem;
        }

        .task-main-tabs .nav-link i {
            font-size: 1rem;
            margin-right: 0.5rem;
        }
    }

    /* Desktop Pagination Controls */
    .pagination-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 400px;
        margin: 0 auto;
        gap: 1rem;
        flex-wrap: nowrap;
    }

    /* Mobile Pagination Controls */
    @media (max-width: 767.98px) {
        .pagination-controls {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            gap: 0.15rem;
            flex-wrap: nowrap;
            max-width: none;
            margin: 0;
        }
    }

    /* Task Container Enhancements */
    .task-container-enhanced {
        position: relative;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    /* Desktop Task Container */
    @media (min-width: 768px) {
        .task-container-enhanced {
            max-height: calc(100vh - 200px);
        }

        .task-content-scrollable {
            max-height: calc(100vh - 350px);
            overflow-y: auto;
        }
    }

    /* Enhanced header strip with elegant colors */
    .task-header-enhanced::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #3498db, #17a2b8, #20c997);
    }

    .task-card-enhanced {
        margin-bottom: 1rem;
        max-height: none;
    }

    /* Enhanced task details with better spacing */
    .task-details-enhanced {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .task-detail-item {
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #17a2b8;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .task-detail-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .task-detail-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
    }

    .task-detail-value {
        color: #212529;
        font-size: 1rem;
        line-height: 1.4;
        word-wrap: break-word;
    }

    .task-description-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 1rem;
        border-radius: 6px;
        margin-top: 1rem;
    }

    .task-detail-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .task-detail-value {
        color: #212529;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        .workorder-detail-container {
            padding: 15px;
        }

        .workorder-header {
            padding: 20px;
            margin-bottom: 20px;
        }

        .workorder-header h1 {
            font-size: 1.8rem;
        }

        .nav-tabs-enhanced {
            padding: 6px;
            margin-bottom: 1rem;
        }

        .nav-tabs-enhanced .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            margin: 0 2px;
        }

        .nav-tabs-enhanced .nav-link i {
            font-size: 1.1rem;
            margin-right: 0.5rem;
        }

        .tab-pane-enhanced {
            padding: 1.5rem;
            min-height: 300px;
        }

        .detail-row-enhanced {
            flex-direction: column;
            align-items: flex-start;
            padding: 1.25rem 0;
        }

        .detail-label-enhanced {
            font-size: 1.2rem;
            min-width: auto;
            margin-right: 0;
            margin-bottom: 0.5rem;
        }

        .detail-label-enhanced i {
            font-size: 1.4rem;
            margin-right: 0.75rem;
        }

        .detail-value-enhanced {
            font-size: 1.1rem;
            text-align: left;
            padding-left: 2.5rem;
        }

        .task-header-enhanced {
            padding: 1rem;
            position: sticky;
            top: 60px;
            z-index: 100;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .task-body-enhanced {
            padding: 1.25rem;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
            -webkit-overflow-scrolling: touch;
        }

        .task-actions-enhanced {
            flex-direction: column;
            padding: 1rem;
        }

        .task-action-btn {
            min-width: 100%;
            padding: 1rem;
            font-size: 1rem;
        }

        .pagination-controls {
            flex-direction: column;
            gap: 0.75rem;
        }

        .pagination-btn {
            padding: 1rem 1.5rem;
            font-size: 1rem;
        }

        .pagination-info {
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
            text-align: center;
        }

        /* Mobile scroll optimization */
        .tab-content-enhanced {
            max-height: 70vh;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .task-card-enhanced {
            margin-bottom: 2rem;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 120px);
        }

        .task-card-enhanced .task-body-enhanced {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
    }

    /* Enhanced Status Badge Styles with Better Contrast */
    .status-badge {
        font-weight: 600;
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .status-ASSIGN {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    }
    .status-APPR {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }
    .status-READY {
        background: linear-gradient(135deg, #20c997, #17a2b8);
        color: white;
        box-shadow: 0 2px 4px rgba(32, 201, 151, 0.3);
    }
    .status-INPRG {
        background: linear-gradient(135deg, #fd7e14, #e55a00);
        color: white;
        box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
    }
    .status-WMATL {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WAPPR {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WGOVT {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WSERV {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WSCH {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-COMP {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }
    .status-CLOSE {
        background: linear-gradient(135deg, #343a40, #23272b);
        color: white;
        box-shadow: 0 2px 4px rgba(52, 58, 64, 0.3);
    }
    .status-PACK {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }
    .status-DEFER {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }

    /* Task Main Tabs */
    .task-main-tabs {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 6px;
        margin: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-bottom: none;
    }

    .task-main-tabs .nav-link {
        border: none;
        border-radius: 6px;
        padding: 0.75rem 1rem;
        margin: 0 3px;
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
        background: transparent;
        display: flex;
        align-items: center;
    }

    .task-main-tabs .nav-link:hover {
        background: rgba(52, 152, 219, 0.1);
        color: #3498db;
        transform: translateY(-1px);
    }

    .task-main-tabs .nav-link.active {
        background: linear-gradient(135deg, #3498db, #17a2b8);
        color: white;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        transform: translateY(-1px);
    }

    .task-main-tabs .nav-link i {
        font-size: 1.1rem;
        margin-right: 0.75rem;
        transition: transform 0.3s ease;
    }

    .task-main-tabs .nav-link:hover i {
        transform: scale(1.1);
    }

    .task-main-tabs .nav-link.active i {
        transform: scale(1.15);
    }

    /* Task Tab Content */
    .task-main-tabs + .tab-content {
        background: white;
        border-radius: 0 0 8px 8px;
        min-height: 300px;
    }

    .task-main-tabs + .tab-content .tab-pane {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Enhanced form controls in tabs */
    .task-main-tabs + .tab-content .form-select,
    .task-main-tabs + .tab-content .btn {
        transition: all 0.3s ease;
    }

    .task-main-tabs + .tab-content .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .task-main-tabs + .tab-content .btn:hover {
        transform: translateY(-1px);
    }

    /* Task Sub-tabs for Materials and Labor */
    .task-sub-tabs {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 6px;
        margin: 1rem 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .task-sub-tabs .nav-link {
        border: none;
        border-radius: 6px;
        padding: 0.75rem 1rem;
        margin: 0 3px;
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
        background: transparent;
    }

    .task-sub-tabs .nav-link:hover {
        background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
        color: #495057;
        transform: translateY(-1px);
    }

    .task-sub-tabs .nav-link.active {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
    }

    .task-sub-content {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        min-height: 200px;
    }

    .task-action-section {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 2px solid #e9ecef;
    }

    /* Enhanced Material Card Styles */
    .material-card-enhanced {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .material-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #007bff, #28a745, #17a2b8);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .material-card-enhanced:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #007bff;
    }

    .material-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .material-card-header-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }

    .material-item-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .material-icon {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }

    .material-card-enhanced:hover .material-icon {
        transform: rotate(10deg) scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .material-itemnum-enhanced {
        font-weight: 700;
        color: #007bff;
        font-size: 1.1rem;
        margin: 0;
    }

    .material-description-enhanced {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
        line-height: 1.4;
    }

    .material-qty-badge {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .material-card-enhanced:hover .material-qty-badge {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    .material-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .material-detail-item-enhanced {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .material-detail-item-enhanced:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .material-detail-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        transition: all 0.3s ease;
    }

    .material-detail-item-enhanced:hover .material-detail-icon {
        transform: scale(1.1);
    }

    .material-detail-content {
        flex: 1;
    }

    .material-detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 0.8rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .material-detail-value-enhanced {
        color: #212529;
        font-weight: 500;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    /* Enhanced Labor Card Styles */
    .labor-card-enhanced {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .labor-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #fd7e14, #ffc107, #20c997);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .labor-card-enhanced:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #fd7e14;
    }

    .labor-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .labor-card-header-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }

    .labor-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .labor-icon {
        background: linear-gradient(135deg, #fd7e14, #e55a00);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }

    .labor-card-enhanced:hover .labor-icon {
        transform: rotate(-10deg) scale(1.1);
        box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
    }

    .labor-code-enhanced {
        font-weight: 700;
        color: #fd7e14;
        font-size: 1.1rem;
        margin: 0;
    }

    .labor-craft-enhanced {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    .labor-hours-badge {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .labor-card-enhanced:hover .labor-hours-badge {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
    }

    .labor-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .labor-detail-item-enhanced {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .labor-detail-item-enhanced:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .labor-detail-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        transition: all 0.3s ease;
    }

    .labor-detail-item-enhanced:hover .labor-detail-icon {
        transform: scale(1.1);
    }

    .labor-detail-content {
        flex: 1;
    }

    .labor-detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 0.8rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .labor-detail-value-enhanced {
        color: #212529;
        font-weight: 500;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    .labor-actions-enhanced {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .labor-action-btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        border: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .labor-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .priority-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .priority-1 { background-color: #e74c3c; }
    .priority-2 { background-color: #f39c12; }
    .priority-3 { background-color: #f1c40f; }
    .priority-4 { background-color: #2ecc71; }
    .priority-5 { background-color: #95a5a6; }

    .detail-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }


    .detail-card-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }

    .detail-card-body {
        padding: 20px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 15px;
        align-items: center;
    }

    .detail-label {
        font-weight: 600;
        color: #6c757d;
        min-width: 150px;
        margin-right: 15px;
    }

    .detail-value {
        color: #495057;
        flex: 1;
    }

    .detail-value.empty {
        color: #adb5bd;
        font-style: italic;
    }

    .performance-info {
        background: #e8f5e8;
        border: 1px solid #c3e6c3;
        border-radius: 5px;
        padding: 10px;
        margin-top: 20px;
        font-size: 0.9rem;
        color: #155724;
    }

    .task-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 15px;
        overflow: hidden;
        transition: box-shadow 0.2s ease;
    }

    .task-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .task-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .task-header .d-flex {
        min-height: 40px;
    }

    .task-body {
        padding: 15px 20px;
    }

    .task-title {
        font-weight: 600;
        color: #495057;
        margin: 0;
        flex: 1;
    }

    .task-actions {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .status-dropdown {
        min-width: 120px;
    }

    .update-status-btn {
        padding: 5px 15px;
        font-size: 0.875rem;
    }

    .task-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 10px;
    }

    .task-info-item {
        display: flex;
        flex-direction: column;
    }

    .task-info-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 2px;
    }

    .task-info-value {
        font-weight: 500;
        color: #495057;
    }

    .no-tasks {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
        font-style: italic;
    }

    .icon {
        margin-right: 8px;
        color: #6c757d;
    }

    .planned-materials-section {
        border-top: 1px solid #dee2e6;
        padding-top: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }

    .planned-materials-section h6 {
        color: #495057;
        font-weight: 600;
    }

    .btn-group .btn {
        font-size: 0.875rem;
    }

    .materials-content {
        max-height: 500px;
        overflow-y: auto;
    }

    /* Desktop List View */
    .materials-desktop-view {
        display: block;
    }

    .materials-mobile-view {
        display: none;
    }

    .material-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.85rem;
    }

    .material-table th,
    .material-table td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
        vertical-align: top;
    }

    .material-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .material-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .material-itemnum {
        font-weight: 600;
        color: #0d6efd;
    }

    .material-description {
        max-width: 200px;
        word-wrap: break-word;
    }

    .material-cost {
        font-weight: 500;
        color: #198754;
    }

    .material-qty {
        font-weight: 500;
        color: #fd7e14;
    }

    /* Mobile Card View */
    @media (max-width: 768px) {
        .materials-desktop-view {
            display: none;
        }

        .materials-mobile-view {
            display: block;
        }

        .material-card {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .material-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .material-card-itemnum {
            font-weight: 600;
            color: #0d6efd;
            font-size: 1rem;
        }

        .material-card-qty {
            font-weight: 500;
            color: #fd7e14;
            font-size: 0.9rem;
        }

        .material-card-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .material-card-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 0.8rem;
        }

        .material-card-detail {
            display: flex;
            flex-direction: column;
        }

        .material-card-detail-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 2px;
        }

        .material-card-detail-value {
            color: #6c757d;
        }
    }

    .material-detail-item {
        display: flex;
        flex-direction: column;
    }

    .material-detail-label {
        color: #6c757d;
        font-size: 0.75rem;
        margin-bottom: 1px;
    }

    .material-detail-value {
        color: #495057;
        font-weight: 500;
    }

    .load-materials-btn {
        transition: all 0.2s ease;
    }

    .load-materials-btn:hover {
        transform: translateY(-1px);
    }

    .materials-loading {
        text-align: center;
        padding: 20px;
        color: #6c757d;
    }

    .materials-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 10px;
        border-radius: 5px;
        text-align: center;
    }

    .materials-empty {
        text-align: center;
        padding: 15px;
        color: #6c757d;
        font-style: italic;
    }

    /* Material Request Modal Styles */
    .item-result-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 8px;
        transition: background-color 0.2s ease;
    }

    .item-result-card:hover {
        background: #e9ecef;
    }

    .item-results-container {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        background: white;
    }

    .request-material-btn {
        transition: all 0.2s ease;
    }

    .request-material-btn:hover {
        transform: translateY(-1px);
    }

    /* ===== COMPREHENSIVE RESPONSIVE DESIGN ===== */

    /* Extra Small Devices (phones, 576px and down) */
    @media (max-width: 575.98px) {
        .workorder-detail-container {
            padding: 8px;
            margin: 0;
        }

        .workorder-header {
            padding: 15px;
            margin-bottom: 15px;
        }

        .workorder-header .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 10px;
        }

        .workorder-header h1 {
            font-size: 1.5rem;
            line-height: 1.3;
        }

        .workorder-header .subtitle {
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .status-badge {
            padding: 6px 12px;
            font-size: 0.75rem;
        }

        .detail-card {
            margin-bottom: 15px;
        }

        .detail-card-header {
            padding: 12px 15px;
            font-size: 0.9rem;
        }

        .detail-card-body {
            padding: 15px;
        }

        .detail-row {
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .detail-label {
            min-width: auto;
            margin-bottom: 4px;
            font-size: 0.85rem;
            font-weight: 700;
        }

        .detail-value {
            font-size: 0.9rem;
        }

        /* Task Cards Mobile */
        .task-card {
            margin-bottom: 12px;
        }

        .task-header {
            padding: 12px 15px;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .task-title {
            font-size: 0.9rem;
            line-height: 1.4;
            margin: 0;
            order: 2;
        }

        .toggle-task-btn {
            order: 1;
            align-self: flex-end;
            margin-bottom: 5px;
        }

        .task-actions {
            order: 3;
            width: 100%;
            flex-direction: column;
            gap: 8px;
        }

        .status-dropdown {
            min-width: 100%;
            font-size: 0.85rem;
        }

        .update-status-btn {
            width: 100%;
            font-size: 0.85rem;
        }

        .task-body {
            padding: 12px 15px;
        }

        .task-info {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .task-info-item {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .task-info-label {
            font-size: 0.75rem;
        }

        .task-info-value {
            font-size: 0.85rem;
        }

        /* Materials Section Mobile */
        .planned-materials-section {
            padding: 12px;
            margin-top: 12px;
        }

        .planned-materials-section .d-flex {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .planned-materials-section h6 {
            font-size: 0.9rem;
        }

        .btn-group {
            width: 100%;
            flex-direction: column;
        }

        .btn-group .btn {
            width: 100%;
            margin-bottom: 5px;
            font-size: 0.8rem;
        }

        /* Back Button Mobile */
        .back-button {
            margin-bottom: 15px;
        }

        .back-button .btn {
            font-size: 0.85rem;
            padding: 8px 12px;
        }
    }

    /* Small Devices (landscape phones, 576px and up) */
    @media (min-width: 576px) and (max-width: 767.98px) {
        .workorder-detail-container {
            padding: 12px;
        }

        .workorder-header {
            padding: 20px;
        }

        .workorder-header h1 {
            font-size: 1.8rem;
        }

        .task-header {
            flex-wrap: wrap;
            gap: 10px;
        }

        .task-title {
            flex: 1;
            min-width: 200px;
        }

        .task-actions {
            flex-wrap: wrap;
            gap: 8px;
        }

        .status-dropdown {
            min-width: 140px;
        }

        .task-info {
            grid-template-columns: repeat(2, 1fr);
        }

        .planned-materials-section .d-flex {
            flex-wrap: wrap;
            gap: 10px;
        }

        .btn-group {
            flex-wrap: wrap;
        }
    }

    /* Medium Devices (tablets, 768px and up) */
    @media (min-width: 768px) and (max-width: 991.98px) {
        .workorder-detail-container {
            padding: 15px;
        }

        .workorder-header h1 {
            font-size: 2.2rem;
        }

        .task-info {
            grid-template-columns: repeat(2, 1fr);
        }

        .task-actions {
            flex-wrap: wrap;
        }
    }

    /* Large Devices (desktops, 992px and up) */
    @media (min-width: 992px) and (max-width: 1199.98px) {
        .workorder-detail-container {
            max-width: 1000px;
        }

        .task-info {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* Extra Large Devices (large desktops, 1200px and up) */
    @media (min-width: 1200px) {
        .workorder-detail-container {
            max-width: 1200px;
        }

        .task-info {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    /* Modal Responsive */
    @media (max-width: 575.98px) {
        .modal-dialog {
            margin: 10px;
            max-width: calc(100% - 20px);
        }

        .modal-body {
            padding: 15px;
        }

        .modal-header {
            padding: 15px;
        }

        .modal-footer {
            padding: 15px;
            flex-direction: column;
            gap: 10px;
        }

        .modal-footer .btn {
            width: 100%;
        }

        .row .col-md-8,
        .row .col-md-4,
        .row .col-md-6 {
            margin-bottom: 15px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="workorder-detail-container" data-site-id="{{ user_site_id if user_site_id else 'UNKNOWN' }}">
    <!-- Back Button -->
    <div class="back-button">
        <a href="{{ url_for('enhanced_workorders') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Work Orders
        </a>
    </div>

    <!-- Work Order Header -->
    <div class="workorder-header">
        <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start">
            <div class="flex-grow-1">
                <h1 class="mb-2">
                    <span class="priority-indicator priority-{{ workorder.priority or 3 }}"></span>
                    <span class="d-inline d-sm-inline">Work Order {{ workorder.wonum }}</span>
                </h1>
                <div class="subtitle">{{ workorder.description or 'No description available' }}</div>
            </div>
            <div class="mt-2 mt-sm-0">
                <span class="status-badge status-{{ workorder.status }}">{{ workorder.status or 'Unknown' }}</span>
            </div>
        </div>
    </div>

    <!-- Enhanced Tab Navigation -->
    <ul class="nav nav-tabs nav-tabs-enhanced" id="workorderTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab" aria-controls="basic" aria-selected="true">
                <i class="fas fa-info-circle"></i>Basic Information
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="location-tab" data-bs-toggle="tab" data-bs-target="#location" type="button" role="tab" aria-controls="location" aria-selected="false">
                <i class="fas fa-map-marker-alt"></i>Location & Asset
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="scheduling-tab" data-bs-toggle="tab" data-bs-target="#scheduling" type="button" role="tab" aria-controls="scheduling" aria-selected="false">
                <i class="fas fa-calendar-alt"></i>Scheduling
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab" aria-controls="tasks" aria-selected="false">
                <i class="fas fa-tasks"></i>Work Order Tasks
                {% if tasks %}
                    <span class="badge bg-light text-dark ms-2">{{ tasks|length }}</span>
                {% endif %}
            </button>
        </li>
    </ul>

    <!-- Enhanced Tab Content -->
    <div class="tab-content tab-content-enhanced" id="workorderTabContent">
        <!-- Basic Information Tab -->
        <div class="tab-pane fade show active tab-pane-enhanced" id="basic" role="tabpanel" aria-labelledby="basic-tab">
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-hashtag" style="color: #fd7e14;"></i>Work Order Number
                </div>
                <div class="detail-value-enhanced">{{ workorder.wonum }}</div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-file-alt" style="color: #28a745;"></i>Description
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.description }}">
                    {{ workorder.description or 'No description available' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-flag" style="color: #17a2b8;"></i>Status
                </div>
                <div class="detail-value-enhanced">
                    <span class="status-badge status-{{ workorder.status }}">{{ workorder.status or 'Unknown' }}</span>
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>Priority
                </div>
                <div class="detail-value-enhanced">
                    <span class="priority-indicator priority-{{ workorder.priority or 3 }}"></span>
                    {{ workorder.priority or 'Not set' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-tools"></i>Work Type
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.worktype }}">
                    {{ workorder.worktype or 'Not specified' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-building"></i>Site
                </div>
                <div class="detail-value-enhanced">{{ workorder.siteid or user_site_id }}</div>
            </div>
        </div>

        <!-- Location & Asset Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="location" role="tabpanel" aria-labelledby="location-tab">
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-map-pin"></i>Location
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.location }}">
                    {{ workorder.location or 'No location specified' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-cog"></i>Asset Number
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.assetnum }}">
                    {{ workorder.assetnum or 'No asset assigned' }}
                </div>
            </div>
        </div>

        <!-- Scheduling Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="scheduling" role="tabpanel" aria-labelledby="scheduling-tab">
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-play-circle"></i>Target Start
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.targetstart }}">
                    {{ workorder.targetstart or 'Not scheduled' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-stop-circle"></i>Target Finish
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.targetfinish }}">
                    {{ workorder.targetfinish or 'Not scheduled' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-calendar-check"></i>Scheduled Start
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.schedstart }}">
                    {{ workorder.schedstart or 'Not scheduled' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-calendar-times"></i>Scheduled Finish
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.schedfinish }}">
                    {{ workorder.schedfinish or 'Not scheduled' }}
                </div>
            </div>
        </div>

        <!-- Tasks Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="tasks" role="tabpanel" aria-labelledby="tasks-tab">
            {% if tasks %}
                <!-- Task Container -->
                <div id="taskContainer" class="task-container-enhanced">
                    <!-- Tasks will be dynamically loaded here by JavaScript -->
                </div>

                <!-- Legacy Task Pagination Controls (Hidden - using sticky navigation now) -->
                <div class="task-pagination d-none">
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prevTaskBtn" onclick="previousTask()" disabled>
                            <i class="fas fa-chevron-left"></i><span class="d-none d-md-inline">Previous</span><span class="d-md-none">Prev</span>
                        </button>
                        <div class="pagination-info">
                            <span id="currentTaskInfo" class="d-none d-md-inline">Task 1 of {{ tasks|length }}</span>
                            <span id="currentTaskInfoMobile" class="d-md-none">1/{{ tasks|length }}</span>
                        </div>
                        <button class="pagination-btn" id="nextTaskBtn" onclick="nextTask()" {% if tasks|length <= 1 %}disabled{% endif %}>
                            <span class="d-none d-md-inline">Next</span><span class="d-md-none">Next</span><i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Tasks Found</h5>
                    <p class="text-muted">This work order doesn't have any associated tasks.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Performance Information -->
    <div class="performance-info mt-4 p-3 bg-light rounded">
        <strong>Performance:</strong>
        Loaded in {{ "%.3f"|format(load_time) }}s using {{ auth_method }}
        {% if tasks %}
        <br><strong>Tasks:</strong> {{ tasks|length }} tasks loaded
        {% endif %}
    </div>

</div>

<!-- Hidden Tasks Data for JavaScript -->
<script type="application/json" id="tasksData">
{{ tasks | tojson | safe }}
</script>





<script>
// Global variables
let currentSiteId = null;
let tasksData = [];
let currentTaskIndex = 0;

// Enhanced task pagination and display functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get site ID from data attribute
    const container = document.querySelector('.workorder-detail-container');
    currentSiteId = container ? container.getAttribute('data-site-id') : 'UNKNOWN';
    console.log('Current Site ID:', currentSiteId);

    // Load tasks data from JSON
    const tasksDataElement = document.getElementById('tasksData');
    if (tasksDataElement) {
        try {
            tasksData = JSON.parse(tasksDataElement.textContent);
            console.log('Loaded tasks data:', tasksData);

            // Initialize task display if tasks exist
            if (tasksData && tasksData.length > 0) {
                displayCurrentTask();
            }
        } catch (error) {
            console.error('Error parsing tasks data:', error);
        }
    }

    // Initialize event handlers for dynamic content
    initializeEventHandlers();
});

// Task pagination functions
function nextTask() {
    if (currentTaskIndex < tasksData.length - 1) {
        currentTaskIndex++;
        displayCurrentTask();
    }
}

function previousTask() {
    if (currentTaskIndex > 0) {
        currentTaskIndex--;
        displayCurrentTask();
    }
}

function displayCurrentTask() {
    if (!tasksData || tasksData.length === 0) return;

    const task = tasksData[currentTaskIndex];
    const container = document.getElementById('taskContainer');

    if (!container) return;

    // Generate task card HTML
    container.innerHTML = generateTaskCardHTML(task);

    // Update navigation info and buttons within the task card
    const taskWonum = task.wonum;

    // Update pagination info for both desktop and mobile within the task
    const desktopInfo = document.getElementById(`currentTaskInfo-${taskWonum}`);
    const mobileInfo = document.getElementById(`currentTaskInfoMobile-${taskWonum}`);

    if (desktopInfo) {
        desktopInfo.textContent = `Task ${currentTaskIndex + 1} of ${tasksData.length}`;
    }
    if (mobileInfo) {
        mobileInfo.textContent = `${currentTaskIndex + 1}/${tasksData.length}`;
    }

    // Update navigation buttons within the task
    const prevBtn = document.getElementById(`prevTaskBtn-${taskWonum}`);
    const nextBtn = document.getElementById(`nextTaskBtn-${taskWonum}`);

    if (prevBtn) {
        prevBtn.disabled = currentTaskIndex === 0;
    }
    if (nextBtn) {
        nextBtn.disabled = currentTaskIndex === tasksData.length - 1;
    }

    // Also update the old pagination controls if they exist (for backward compatibility)
    const oldDesktopInfo = document.getElementById('currentTaskInfo');
    const oldMobileInfo = document.getElementById('currentTaskInfoMobile');
    const oldPrevBtn = document.getElementById('prevTaskBtn');
    const oldNextBtn = document.getElementById('nextTaskBtn');

    if (oldDesktopInfo) {
        oldDesktopInfo.textContent = `Task ${currentTaskIndex + 1} of ${tasksData.length}`;
    }
    if (oldMobileInfo) {
        oldMobileInfo.textContent = `${currentTaskIndex + 1}/${tasksData.length}`;
    }
    if (oldPrevBtn) {
        oldPrevBtn.disabled = currentTaskIndex === 0;
    }
    if (oldNextBtn) {
        oldNextBtn.disabled = currentTaskIndex === tasksData.length - 1;
    }

    // Reinitialize event handlers for the new content
    initializeEventHandlers();
}

function generateTaskCardHTML(task) {
    return `
        <div class="task-card-enhanced">
            <!-- Sticky Task Header -->
            <div class="task-header-sticky">
                <h5>
                    <i class="fas fa-clipboard-list me-2"></i>
                    Task WO: ${task.wonum}
                </h5>
                <div class="task-meta">
                    <span class="badge">
                        <i class="fas fa-hashtag me-1"></i>ID: ${task.taskid}
                    </span>
                    <span class="status-badge status-${task.status}">${task.status || 'Unknown'}</span>
                </div>

                <!-- Description in Sticky Header -->
                <div class="task-description-sticky">
                    <i class="fas fa-file-alt me-2"></i>
                    <strong>Description:</strong> ${task.description || 'No description available'}
                </div>
            </div>

            <!-- Scrollable Content Area -->
            <div class="task-content-scrollable">
                <!-- Task Main Tabs -->
                <ul class="nav nav-tabs task-main-tabs" id="taskMainTabs-${task.wonum}" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="details-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#details-${task.wonum}" type="button" role="tab">
                            <i class="fas fa-info-circle"></i>Details
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="status-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#status-${task.wonum}" type="button" role="tab">
                            <i class="fas fa-edit"></i>Change Status
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="labor-main-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#labor-main-${task.wonum}" type="button" role="tab">
                            <i class="fas fa-users"></i>Load Labor
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="search-labor-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#search-labor-${task.wonum}" type="button" role="tab">
                            <i class="fas fa-search"></i>Search Labor
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="materials-main-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#materials-main-${task.wonum}" type="button" role="tab">
                            <i class="fas fa-boxes"></i>Materials
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="taskMainTabContent-${task.wonum}">
                    <!-- Details Tab -->
                    <div class="tab-pane fade show active" id="details-${task.wonum}" role="tabpanel">
                        <div class="p-3">`

                            <!-- Task Details Grid with Better Spacing -->
                            <div class="task-details-enhanced">
                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-tools me-2" style="color: #17a2b8;"></i>Work Type
                                    </div>
                                    <div class="task-detail-value">${task.worktype || 'Not specified'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-user me-2" style="color: #3498db;"></i>Owner
                                    </div>
                                    <div class="task-detail-value">${task.owner || 'Not Assigned'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-users me-2" style="color: #6f42c1;"></i>Owner Group
                                    </div>
                                    <div class="task-detail-value">${task.owner_group || 'Not Assigned'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-user-tie me-1" style="color: #ffc107;"></i>Lead
                                    </div>
                                    <div class="task-detail-value">${task.lead || 'Not Assigned'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-user-check me-1" style="color: #e83e8c;"></i>Supervisor
                                    </div>
                                    <div class="task-detail-value">${task.supervisor || 'Not Assigned'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-users-cog me-1" style="color: #dc3545;"></i>Crew
                                    </div>
                                    <div class="task-detail-value">${task.crew || 'Not Assigned'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-map-pin me-1" style="color: #20c997;"></i>Location
                                    </div>
                                    <div class="task-detail-value">${task.location || 'Not specified'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-cog me-1" style="color: #6c757d;"></i>Asset
                                    </div>
                                    <div class="task-detail-value">${task.assetnum || 'No asset'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-calendar-check me-1" style="color: #28a745;"></i>Scheduled Start
                                    </div>
                                    <div class="task-detail-value">${task.schedstart || 'Not scheduled'}</div>
                                </div>

                                <div class="task-detail-item">
                                    <div class="task-detail-label">
                                        <i class="fas fa-clock me-1" style="color: #fd7e14;"></i>Estimated Duration
                                    </div>
                                    <div class="task-detail-value">${task.estdur || 'Not estimated'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Change Status Tab -->
                    <div class="tab-pane fade" id="status-${task.wonum}" role="tabpanel">
                        <div class="p-3">
                            <h6 class="mb-3">
                                <i class="fas fa-edit me-2 text-primary"></i>Change Task Status
                            </h6>
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label class="form-label">Select New Status:</label>
                                    <select class="form-select status-dropdown" data-task-wonum="${task.wonum}" data-current-status="${task.status}">
                                        <option value="">Choose status...</option>
                                        <option value="ASSIGN" ${task.status === 'ASSIGN' ? 'disabled' : ''}>Assign</option>
                                        <option value="APPR" ${task.status === 'APPR' ? 'disabled' : ''}>Approve</option>
                                        <option value="INPRG" ${task.status === 'INPRG' ? 'disabled' : ''}>In Progress</option>
                                        <option value="WMATL" ${task.status === 'WMATL' ? 'disabled' : ''}>Waiting for Material</option>
                                        <option value="COMP" ${task.status === 'COMP' ? 'disabled' : ''}>Complete</option>
                                        <option value="CLOSE" ${task.status === 'CLOSE' ? 'disabled' : ''}>Close</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-primary w-100 update-status-btn" data-task-wonum="${task.wonum}" disabled>
                                        <i class="fas fa-save me-2"></i>Update Status
                                    </button>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Current status: <strong>${task.status || 'Unknown'}</strong>
                            </div>
                        </div>
                    </div>

                    <!-- Load Labor Tab -->
                    <div class="tab-pane fade" id="labor-main-${task.wonum}" role="tabpanel">
                        <div class="p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-2 text-secondary"></i>Labor Resources
                                </h6>
                                <button class="btn btn-outline-info btn-sm load-labor-btn" data-task-wonum="${task.wonum}" data-task-status="${task.status}">
                                    <i class="fas fa-download me-1"></i>Load Labor
                                </button>
                            </div>
                            <div id="labor-content-${task.wonum}" class="labor-content"
                                 data-task-id="${task.taskid}"
                                 data-site-id="${task.siteid}"
                                 data-task-wonum="${task.wonum}"
                                 data-parent-wonum="${task.parent || task.wonum}">
                                <div class="text-center py-4 text-muted">
                                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                                    <p>Click "Load Labor" to view labor assignments for this task</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Labor Tab -->
                    <div class="tab-pane fade" id="search-labor-${task.wonum}" role="tabpanel">
                        <div class="p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-search me-2 text-primary"></i>Search Labor Resources
                                </h6>
                                ${['APPR', 'ASSIGN', 'WMATL', 'INPRG', 'READY', 'COMP'].includes(task.status) ? `
                                <button class="btn btn-outline-primary btn-sm search-labor-btn" onclick="openLaborSearchForTask('${task.siteid || 'UNKNOWN'}', '${task.parent || task.wonum}', '${task.wonum}', ${task.taskid})">
                                    <i class="fas fa-search me-1"></i>Search Labor
                                </button>
                                ` : ''}
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Use this section to search and assign labor resources to the task.
                                ${!['APPR', 'ASSIGN', 'WMATL', 'INPRG', 'READY', 'COMP'].includes(task.status) ?
                                    '<br><strong>Note:</strong> Labor search is not available for current task status.' : ''}
                            </div>
                        </div>
                    </div>

                    <!-- Materials Tab -->
                    <div class="tab-pane fade" id="materials-main-${task.wonum}" role="tabpanel">
                        <div class="p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-boxes me-2 text-info"></i>Planned Materials
                                </h6>
                                <div class="btn-group">
                                    <button class="btn btn-outline-info btn-sm load-materials-btn" data-task-wonum="${task.wonum}" data-task-status="${task.status}">
                                        <i class="fas fa-download me-1"></i>Load Materials
                                    </button>
                                    ${['APPR', 'ASSIGN', 'WMATL', 'INPRG'].includes(task.status) ? `
                                    <button class="btn btn-outline-success btn-sm search-inventory-btn" onclick="openInventorySearchForTask('${task.siteid || 'UNKNOWN'}', '${task.parent || task.wonum}', '${task.wonum}', ${task.taskid})">
                                        <i class="fas fa-search me-1"></i>Search Inventory
                                    </button>
                                    ` : ''}
                                </div>
                            </div>
                            <div id="materials-content-${task.wonum}" class="materials-content">
                                <div class="text-center py-4 text-muted">
                                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                                    <p>Click "Load Materials" to view planned materials for this task</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sticky Navigation at Bottom -->
            <div class="task-navigation-sticky">
                <div class="task-navigation-controls">
                    <button class="task-nav-btn" id="prevTaskBtn-${task.wonum}" onclick="previousTask()" disabled>
                        <i class="fas fa-chevron-left"></i>
                        <span class="d-none d-md-inline">Previous</span>
                        <span class="d-md-none">Prev</span>
                    </button>
                    <div class="task-nav-info">
                        <span id="currentTaskInfo-${task.wonum}" class="d-none d-md-inline">Task 1 of ${tasksData ? tasksData.length : 1}</span>
                        <span id="currentTaskInfoMobile-${task.wonum}" class="d-md-none">1/${tasksData ? tasksData.length : 1}</span>
                    </div>
                    <button class="task-nav-btn" id="nextTaskBtn-${task.wonum}" onclick="nextTask()">
                        <span class="d-none d-md-inline">Next</span>
                        <span class="d-md-none">Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

function initializeEventHandlers() {
    // Handle status update button clicks
    document.querySelectorAll('.update-status-btn').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const dropdown = document.querySelector(`select[data-task-wonum="${taskWonum}"]`);
            const newStatus = dropdown.value;

            if (!newStatus) {
                alert('Please select a status first');
                return;
            }

            updateTaskStatus(taskWonum, newStatus, this);
        });
    });

    // Handle dropdown change to enable/disable update button
    document.querySelectorAll('.status-dropdown').forEach(dropdown => {
        dropdown.addEventListener('change', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const button = document.querySelector(`button[data-task-wonum="${taskWonum}"]`);
            if (button) {
                button.disabled = !this.value;
            }
        });
    });

    // Handle planned materials loading
    document.querySelectorAll('.load-materials-btn').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const taskStatus = this.getAttribute('data-task-status');
            loadPlannedMaterials(taskWonum, taskStatus, this);
        });
    });

    // Handle labor loading
    document.querySelectorAll('.load-labor-btn').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const taskStatus = this.getAttribute('data-task-status');
            loadTaskLabor(taskWonum, taskStatus, this);
        });
    });
}

// Global function to open inventory search for a specific task
function openInventorySearchForTask(siteId, parentWonum, taskWonum, taskId) {
    // Store task context for material requests
    // parentWonum = parent work order (e.g. 2021-1744762)
    // taskWonum = task work order number (e.g. 2021-1835482)
    // taskId = actual numeric task ID from the task (e.g. 10, 20, 30, etc.)
    if (typeof materialRequestManager !== 'undefined') {
        materialRequestManager.setTaskContext(parentWonum, taskWonum, taskId);
    }

    // Call the regular inventory search
    openInventorySearch(siteId);
}

// Global function to open labor search for a specific task
function openLaborSearchForTask(siteId, parentWonum, taskWonum, taskId) {
    // Store task context for labor requests
    // parentWonum = parent work order (e.g. 2021-1744762)
    // taskWonum = task work order number (e.g. 2021-1835482)
    // taskId = actual numeric task ID from the task (e.g. 10, 20, 30, etc.)
    if (typeof laborSearchManager !== 'undefined') {
        laborSearchManager.setTaskContext(parentWonum, taskWonum, taskId);
    }

    // Call the regular labor search
    openLaborSearch(siteId);
}

// Utility function to format currency properly - USE ONLY REAL MAXIMO DATA
function formatCurrency(amount, currencyCode) {
    // If no real data from Maximo, return "No cost data"
    if (!currencyCode || amount === null || amount === undefined) {
        return 'No cost data';
    }

    const value = parseFloat(amount);
    if (isNaN(value)) {
        return 'No cost data';
    }

    if (currencyCode === 'USD') {
        return `$${value.toFixed(2)}`;
    } else if (currencyCode === 'EUR') {
        return `€${value.toFixed(2)}`;
    } else if (currencyCode === 'GBP') {
        return `£${value.toFixed(2)}`;
    } else {
        return `${currencyCode} ${value.toFixed(2)}`;
    }
}

// Function to format date/time values
function formatDateTime(dateString) {
    if (!dateString) return 'Not set';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) {
        return dateString; // Return as-is if parsing fails
    }
}

// Function to generate comprehensive materials display
function generateMaterialsDisplay(materials) {
    // Desktop table view
    let desktopHtml = `
        <div class="materials-desktop-view">
            <table class="material-table">
                <thead>
                    <tr>
                        <th>Item Number</th>
                        <th>Description</th>
                        <th>Qty</th>
                        <th>Unit Cost</th>
                        <th>Line Cost</th>
                        <th>Vendor</th>
                        <th>Store Location</th>
                        <th>Direct Request</th>
                        <th>Requested By</th>
                        <th>Required Date</th>
                    </tr>
                </thead>
                <tbody>
    `;

    materials.forEach(material => {
        desktopHtml += `
            <tr>
                <td class="material-itemnum">${material.itemnum || 'N/A'}</td>
                <td class="material-description" title="${material.description_longdescription || material.description || 'No description'}">${material.description || 'No description'}</td>
                <td class="material-qty">${material.itemqty || 0} ${material.unit || 'EA'}</td>
                <td class="material-cost">${formatCurrency(material.unitcost || 0, 'USD')}</td>
                <td class="material-cost">${formatCurrency(material.linecost || 0, 'USD')}</td>
                <td>${material.vendor || 'N/A'}</td>
                <td>${material.storeloc || 'N/A'}</td>
                <td>${material.directreq ? '<span class="badge bg-warning">Yes</span>' : '<span class="badge bg-success">No</span>'}</td>
                <td>${material.requestby || 'N/A'}</td>
                <td>${formatDateTime(material.requiredate)}</td>
            </tr>
        `;
    });

    desktopHtml += `
                </tbody>
            </table>
        </div>
    `;

    // Mobile card view
    let mobileHtml = `<div class="materials-mobile-view">`;

    materials.forEach(material => {
        mobileHtml += `
            <div class="material-card-enhanced">
                <div class="material-card-header-enhanced">
                    <div class="material-item-info">
                        <div class="material-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div>
                            <h6 class="material-itemnum-enhanced">${material.itemnum || 'N/A'}</h6>
                            <p class="material-description-enhanced">${material.description || 'No description available'}</p>
                        </div>
                    </div>
                    <div class="material-qty-badge">
                        <i class="fas fa-boxes"></i>
                        ${material.itemqty || 0} ${material.unit || 'EA'}
                    </div>
                </div>

                <div class="material-details-grid">
                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Unit Cost</p>
                            <p class="material-detail-value-enhanced">${formatCurrency(material.unitcost || 0, 'USD')}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Line Cost</p>
                            <p class="material-detail-value-enhanced">${formatCurrency(material.linecost || 0, 'USD')}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Vendor</p>
                            <p class="material-detail-value-enhanced">${material.vendor || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #fd7e14, #e55a00);">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Store Location</p>
                            <p class="material-detail-value-enhanced">${material.storeloc || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, ${material.directreq ? '#dc3545, #c82333' : '#28a745, #1e7e34'});">
                            <i class="fas ${material.directreq ? 'fa-exclamation-triangle' : 'fa-check-circle'}"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Direct Request</p>
                            <p class="material-detail-value-enhanced">${material.directreq ? 'Yes' : 'No'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #6c757d, #495057);">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Requested By</p>
                            <p class="material-detail-value-enhanced">${material.requestby || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #e83e8c, #d91a72);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Required Date</p>
                            <p class="material-detail-value-enhanced">${formatDateTime(material.requiredate)}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #20c997, #17a2b8);">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Line Type</p>
                            <p class="material-detail-value-enhanced">${material.linetype_description || material.linetype || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Condition Code</p>
                            <p class="material-detail-value-enhanced">${material.conditioncode || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <i class="fas fa-bookmark"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Reservation Type</p>
                            <p class="material-detail-value-enhanced">${material.restype_description || material.restype || 'N/A'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    mobileHtml += `</div>`;

    return desktopHtml + mobileHtml;
}

function updateTaskStatus(taskWonum, newStatus, button) {
    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
    button.disabled = true;

    // Make API call
    fetch(`/api/task/${taskWonum}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the status badge
            const statusBadge = button.closest('.task-header').querySelector('.status-badge');
            statusBadge.className = `status-badge status-${newStatus}`;
            statusBadge.textContent = newStatus;

            // Reset dropdown
            const dropdown = document.querySelector(`select[data-task-wonum="${taskWonum}"]`);
            dropdown.value = '';

            // Show success message
            showNotification('success', data.message);
        } else {
            showNotification('error', data.error || 'Failed to update task status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('error', 'Network error occurred while updating task status');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function loadPlannedMaterials(taskWonum, taskStatus, button) {
    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    button.disabled = true;

    const materialsContent = document.getElementById(`materials-content-${taskWonum}`);

    // Show loading in content area
    materialsContent.innerHTML = `
        <div class="materials-loading">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <div>Loading planned materials...</div>
        </div>
    `;

    // Make API call
    fetch(`/api/task/${taskWonum}/planned-materials?status=${taskStatus}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.show_materials) {
            if (data.materials && data.materials.length > 0) {
                // Display materials with comprehensive data
                const materialsHtml = generateMaterialsDisplay(data.materials);
                materialsContent.innerHTML = materialsHtml;

                // Update button to show refresh option
                button.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Refresh';
                button.disabled = false;

                showNotification('success', `Loaded ${data.materials.length} planned materials for task ${taskWonum}`);
            } else {
                // No materials found
                materialsContent.innerHTML = `
                    <div class="materials-empty">
                        <i class="fas fa-box-open fa-2x mb-2"></i>
                        <div>No planned materials found for this task</div>
                    </div>
                `;
                button.innerHTML = originalText;
                button.disabled = false;
            }
        } else {
            // Materials not available for this status
            materialsContent.innerHTML = `
                <div class="materials-empty">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <div>${data.message || 'Planned materials not available for this task status'}</div>
                </div>
            `;
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error loading planned materials:', error);
        materialsContent.innerHTML = `
            <div class="materials-error">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error loading planned materials: ${error.message || 'Unknown error'}
            </div>
        `;
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('error', 'Failed to load planned materials');
    });
}
function loadTaskLabor(taskWonum, taskStatus, button) {
    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    button.disabled = true;

    const laborContent = document.getElementById(`labor-content-${taskWonum}`);

    // Show loading in content area
    laborContent.innerHTML = `
        <div class="labor-loading">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <div>Loading labor assignments...</div>
        </div>
    `;

    // Make API call to get labor data
    fetch(`/api/task/${taskWonum}/labor?status=${taskStatus}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log(`👷 LABOR LOAD: Response for task ${taskWonum}:`, data);

        if (data.success && data.show_labor) {
            if (data.labor && data.labor.length > 0) {
                // Display labor with comprehensive data
                const laborHtml = generateLaborDisplay(data.labor);
                laborContent.innerHTML = laborHtml;

                // Update button to show refresh option
                button.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Refresh';
                button.disabled = false;

                showNotification('success', `Loaded ${data.labor.length} labor assignments for task ${taskWonum}`);
                console.log(`✅ LABOR LOAD: Successfully loaded ${data.labor.length} labor records for task ${taskWonum}`);
            } else {
                // No labor found
                laborContent.innerHTML = `
                    <div class="labor-empty">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <div>No labor assignments found for this task</div>
                        <small class="text-muted">Task ${taskWonum} has no labor records</small>
                    </div>
                `;
                button.innerHTML = originalText;
                button.disabled = false;
                console.log(`ℹ️ LABOR LOAD: No labor records found for task ${taskWonum}`);
            }
        } else {
            // Labor not available or error occurred
            laborContent.innerHTML = `
                <div class="labor-empty">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <div>${data.message || 'Labor assignments not available for this task'}</div>
                    <small class="text-muted">Error: ${data.error || 'Unknown error'}</small>
                </div>
            `;
            button.innerHTML = originalText;
            button.disabled = false;
            console.log(`❌ LABOR LOAD: Failed to load labor for task ${taskWonum}:`, data.error);
        }
    })
    .catch(error => {
        console.error(`❌ LABOR LOAD: Network error loading labor for task ${taskWonum}:`, error);
        laborContent.innerHTML = `
            <div class="labor-error">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error loading labor assignments: ${error.message || 'Network error'}
            </div>
        `;
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('error', 'Failed to load labor assignments');
    });
}

// Function to generate comprehensive labor display
function generateLaborDisplay(laborRecords) {
    // Desktop table view
    let desktopHtml = `
        <div class="labor-desktop-view d-none d-lg-block">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Labor Code</th>
                        <th>Hours</th>
                        <th>Craft</th>
                        <th>Start Date</th>
                        <th>Finish Date</th>
                        <th>Regular Hrs</th>
                        <th>Premium Hrs</th>
                        <th>Transaction Type</th>
                        <th>Trans ID</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    laborRecords.forEach(labor => {
        desktopHtml += `
            <tr>
                <td class="labor-code"><strong>${labor.laborcode || 'N/A'}</strong></td>
                <td class="labor-hours">${labor.laborhrs || 0} hrs</td>
                <td class="labor-craft">${labor.craft || 'N/A'}</td>
                <td class="labor-start">${formatDateTime(labor.startdate)}</td>
                <td class="labor-finish">${formatDateTime(labor.finishdate)}</td>
                <td class="labor-regular">${labor.regularhrs || 0} hrs</td>
                <td class="labor-premium">${labor.premiumpayhours || 0} hrs</td>
                <td class="labor-transtype">${labor.transtype || 'N/A'}</td>
                <td class="labor-transid">${labor.labtransid || 'N/A'}</td>
                <td class="labor-actions">
                    <button class="btn btn-sm btn-outline-warning delete-labor-btn"
                            data-labor-id="${labor.labtransid || ''}"
                            data-labor-code="${labor.laborcode || ''}"
                            data-labor-hours="${labor.regularhrs || 0}"
                            onclick="deleteLaborEntry(this)"
                            title="Add negative hours to offset this entry">
                        <i class="fas fa-minus me-1"></i>Subtract Hours
                    </button>
                </td>
            </tr>
        `;
    });

    desktopHtml += `
                </tbody>
            </table>
        </div>
    `;

    // Mobile card view
    let mobileHtml = `<div class="labor-mobile-view d-lg-none">`;

    laborRecords.forEach(labor => {
        mobileHtml += `
            <div class="labor-card-enhanced">
                <div class="labor-card-header-enhanced">
                    <div class="labor-info">
                        <div class="labor-icon">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <div>
                            <h6 class="labor-code-enhanced">${labor.laborcode || 'N/A'}</h6>
                            <p class="labor-craft-enhanced">${labor.craft || 'No craft specified'}</p>
                        </div>
                    </div>
                    <div class="labor-hours-badge">
                        <i class="fas fa-clock"></i>
                        ${labor.laborhrs || 0} hrs
                    </div>
                </div>

                <div class="labor-details-grid">
                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Regular Hours</p>
                            <p class="labor-detail-value-enhanced">${labor.regularhrs || 0} hrs</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Premium Hours</p>
                            <p class="labor-detail-value-enhanced">${labor.premiumpayhours || 0} hrs</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                            <i class="fas fa-hashtag"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Trans ID</p>
                            <p class="labor-detail-value-enhanced">${labor.labtransid || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Transaction Type</p>
                            <p class="labor-detail-value-enhanced">${labor.transtype || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #fd7e14, #e55a00);">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Start Date</p>
                            <p class="labor-detail-value-enhanced">${formatDateTime(labor.startdate)}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">
                            <i class="fas fa-calendar-minus"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Finish Date</p>
                            <p class="labor-detail-value-enhanced">${formatDateTime(labor.finishdate)}</p>
                        </div>
                    </div>
                </div>

                <div class="labor-actions-enhanced">
                    <button class="labor-action-btn btn-outline-warning delete-labor-btn"
                            data-labor-id="${labor.labtransid || ''}"
                            data-labor-code="${labor.laborcode || ''}"
                            data-labor-hours="${labor.regularhrs || 0}"
                            onclick="deleteLaborEntry(this)"
                            title="Add negative hours to offset this entry">
                        <i class="fas fa-minus"></i>Subtract Hours
                    </button>
                </div>
            </div>
        `;
    });

    mobileHtml += `</div>`;

    return desktopHtml + mobileHtml;
}














function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    let alertClass = 'alert-danger'; // default to error
    if (type === 'success') alertClass = 'alert-success';
    else if (type === 'info') alertClass = 'alert-info';
    else if (type === 'warning') alertClass = 'alert-warning';

    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Global function to refresh materials after material addition
function refreshMaterials() {
    console.log('🔄 Refreshing materials after material addition...');

    // Clear materials cache first
    fetch('/api/task/planned-materials/cache/clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ Materials cache cleared successfully');

            // Refresh all loaded materials sections
            const loadedMaterialsButtons = document.querySelectorAll('.load-materials-btn');
            loadedMaterialsButtons.forEach(button => {
                const taskWonum = button.getAttribute('data-task-wonum');
                const taskStatus = button.getAttribute('data-task-status');
                const materialsContent = document.getElementById(`materials-content-${taskWonum}`);

                // Only refresh if materials were already loaded (not showing the initial load button)
                if (materialsContent && !materialsContent.querySelector('.materials-loading') &&
                    materialsContent.innerHTML.trim() !== '' &&
                    !materialsContent.innerHTML.includes('Load Materials')) {

                    console.log(`🔄 Refreshing materials for task ${taskWonum}`);
                    loadPlannedMaterials(taskWonum, taskStatus, button);
                }
            });

            showNotification('success', 'Materials refreshed successfully');
        } else {
            console.error('❌ Failed to clear materials cache:', data.error);
            showNotification('error', 'Failed to refresh materials cache');
        }
    })
    .catch(error => {
        console.error('❌ Error clearing materials cache:', error);
        showNotification('error', 'Network error while refreshing materials');
    });
}
</script>

<!-- Include Inventory Search Modal -->
{% include 'components/inventory_search_modal.html' %}

<!-- Include Labor Search Modal -->
{% include 'components/labor_search_modal.html' %}

<!-- Include Inventory Search JavaScript -->
<script src="{{ url_for('static', filename='js/inventory_search.js') }}"></script>

<!-- Include Labor Search JavaScript -->
<script src="{{ url_for('static', filename='js/labor_search.js') }}"></script>

{% endblock %}
