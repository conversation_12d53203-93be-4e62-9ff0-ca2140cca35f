<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payload Test - Material Request</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-4">
        <h1>🎯 COMPLETE PAYLOAD TEST</h1>
        <p>This page shows the ENTIRE payload structure being sent to Maximo</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📝 Test Material Request</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label class="form-label">Work Order Number</label>
                                <input type="text" class="form-control" id="wonum" value="2021-1744762">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Site ID</label>
                                <input type="text" class="form-control" id="siteid" value="LCVKWT">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Item Number</label>
                                <input type="text" class="form-control" id="itemnum" value="5975-60-V00-0394">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="quantity" value="1">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Task ID (MANDATORY) - Use actual task wonum</label>
                                <input type="text" class="form-control" id="taskid" value="2021-1744849" placeholder="e.g. 2021-1744849">
                                <small class="form-text text-muted">Use the actual task wonum, not a simple number</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" value="LCVK-CMW-AJ">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Requested By</label>
                                <input type="text" class="form-control" id="requestby" value="TINU.THOMAS">
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="directreq">
                                    <label class="form-check-label" for="directreq">
                                        Direct Request
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" rows="2"></textarea>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="generatePayload()">
                                🎯 Generate Payload
                            </button>
                            <button type="button" class="btn btn-success" onclick="sendToBackend()">
                                🚀 Send to Backend
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📦 COMPLETE PAYLOAD STRUCTURE</h5>
                    </div>
                    <div class="card-body">
                        <h6>Frontend Request Data:</h6>
                        <pre id="frontendPayload" class="bg-light p-3" style="font-size: 12px;"></pre>
                        
                        <h6>Expected Backend Payload to Maximo:</h6>
                        <pre id="backendPayload" class="bg-light p-3" style="font-size: 12px;"></pre>
                        
                        <h6>Material Object Only:</h6>
                        <pre id="materialPayload" class="bg-light p-3" style="font-size: 12px;"></pre>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>📡 API Response</h5>
                    </div>
                    <div class="card-body">
                        <pre id="apiResponse" class="bg-light p-3" style="font-size: 12px;">Click "Send to Backend" to see response...</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function generatePayload() {
            // Get form values
            const wonum = document.getElementById('wonum').value;
            const siteid = document.getElementById('siteid').value;
            const itemnum = document.getElementById('itemnum').value;
            const quantity = parseFloat(document.getElementById('quantity').value);
            const taskid = document.getElementById('taskid').value; // Use string, not number
            const location = document.getElementById('location').value;
            const requestby = document.getElementById('requestby').value;
            const directreq = document.getElementById('directreq').checked;
            const notes = document.getElementById('notes').value;
            
            // Frontend request data
            const frontendData = {
                wonum: wonum,
                siteid: siteid,
                itemnum: itemnum,
                quantity: quantity,
                taskid: taskid,
                location: location || null,
                directreq: directreq,
                notes: notes || null,
                requestby: requestby
            };
            
            // Material object that backend creates
            const materialObject = {
                itemnum: itemnum,
                itemqty: quantity,
                directreq: directreq,
                requestby: requestby,
                taskid: taskid
            };
            
            if (location) {
                materialObject.location = location;
            }
            
            if (notes) {
                materialObject.remarks = notes;
            }
            
            // Complete AddChange payload that backend sends to Maximo
            const backendPayload = [{
                "_action": "AddChange",
                "wonum": wonum,
                "siteid": siteid,
                "description": "Test Work Order Description",
                "status": "APPR",
                "assetnum": "TEST-ASSET",
                "location": "TEST-LOCATION",
                "wpmaterial": [materialObject]
            }];
            
            // Display all payloads
            document.getElementById('frontendPayload').textContent = JSON.stringify(frontendData, null, 2);
            document.getElementById('materialPayload').textContent = JSON.stringify(materialObject, null, 2);
            document.getElementById('backendPayload').textContent = JSON.stringify(backendPayload, null, 2);
        }
        
        async function sendToBackend() {
            // Get form values
            const wonum = document.getElementById('wonum').value;
            const siteid = document.getElementById('siteid').value;
            const itemnum = document.getElementById('itemnum').value;
            const quantity = parseFloat(document.getElementById('quantity').value);
            const taskid = document.getElementById('taskid').value; // Use string, not number
            const location = document.getElementById('location').value;
            const requestby = document.getElementById('requestby').value;
            const directreq = document.getElementById('directreq').checked;
            const notes = document.getElementById('notes').value;
            
            const requestData = {
                wonum: wonum,
                siteid: siteid,
                itemnum: itemnum,
                quantity: quantity,
                taskid: taskid,
                location: location || null,
                directreq: directreq,
                notes: notes || null,
                requestby: requestby
            };
            
            try {
                const response = await fetch('/api/workorder/add-material-request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                document.getElementById('apiResponse').textContent = JSON.stringify(result, null, 2);
                
                if (response.ok) {
                    alert('✅ SUCCESS! Check the backend logs for complete payload details.');
                } else {
                    alert('❌ ERROR! Check the response below.');
                }
            } catch (error) {
                document.getElementById('apiResponse').textContent = `Error: ${error.message}`;
                alert('❌ Network error occurred');
            }
        }
        
        // Generate initial payload on page load
        window.onload = function() {
            generatePayload();
        };
    </script>
</body>
</html>
