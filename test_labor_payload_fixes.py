#!/usr/bin/env python3
"""
Test script to verify labor payload fixes for both regular and negative labor functionality
"""

import re
import os
import json

def test_regular_labor_payload_structure():
    """Test that regular labor addition includes regularhrs field"""
    print("🔍 Testing regular labor payload structure...")
    
    try:
        # Read the labor_request_service.py file
        with open('backend/services/labor_request_service.py', 'r') as f:
            service_content = f.read()
        
        # Check for regularhrs field in new_labor construction
        if '"regularhrs": regularhrs,' in service_content:
            print("✅ regularhrs field found in regular labor payload")
        else:
            print("❌ regularhrs field missing from regular labor payload")
            return False
        
        # Check for the critical comment
        if '# CRITICAL: Include the actual hours value' in service_content:
            print("✅ Critical comment found indicating awareness of the fix")
        else:
            print("❌ Critical comment not found")
            return False
        
        # Check for proper payload structure with _action
        if '"_action": "AddChange"' in service_content:
            print("✅ AddChange action found in payload structure")
        else:
            print("❌ AddChange action missing from payload structure")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing regular labor payload: {e}")
        return False

def test_negative_labor_payload_structure():
    """Test that negative labor addition uses correct payload structure"""
    print("\n🔍 Testing negative labor payload structure...")
    
    try:
        # Read the labor_deletion_service.py file
        with open('backend/services/labor_deletion_service.py', 'r') as f:
            service_content = f.read()
        
        # Check for regularhrs field in negative labor payload
        if '"regularhrs": negative_hours,' in service_content:
            print("✅ regularhrs field found in negative labor payload")
        else:
            print("❌ regularhrs field missing from negative labor payload")
            return False
        
        # Check for genapprservreceipt field (should match regular labor)
        if '"genapprservreceipt": 1,' in service_content:
            print("✅ genapprservreceipt field found in negative labor payload")
        else:
            print("❌ genapprservreceipt field missing from negative labor payload")
            return False
        
        # Check for proper AddChange structure (should be array)
        if 'addchange_payload = [{' in service_content:
            print("✅ AddChange payload is properly structured as array")
        else:
            print("❌ AddChange payload not structured as array")
            return False
        
        # Check for _action field
        if '"_action": "AddChange"' in service_content:
            print("✅ _action field found in negative labor payload")
        else:
            print("❌ _action field missing from negative labor payload")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing negative labor payload: {e}")
        return False

def test_time_conflict_handling():
    """Test that time field conflicts are properly handled"""
    print("\n🔍 Testing time field conflict handling...")
    
    try:
        # Read the labor_request_service.py file
        with open('backend/services/labor_request_service.py', 'r') as f:
            service_content = f.read()
        
        # Check for time conflict warning
        if 'TIME CONFLICT: Both starttime' in service_content:
            print("✅ Time conflict warning found")
        else:
            print("❌ Time conflict warning missing")
            return False
        
        # Check for conditional time handling
        if 'if starttime and finishtime:' in service_content:
            print("✅ Conditional time handling found")
        else:
            print("❌ Conditional time handling missing")
            return False
        
        # Check for Maximo auto-calculation warning
        if 'Maximo may auto-calculate hours and override regularhrs' in service_content:
            print("✅ Maximo auto-calculation warning found")
        else:
            print("❌ Maximo auto-calculation warning missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing time conflict handling: {e}")
        return False

def test_payload_consistency():
    """Test that both services use consistent payload structures"""
    print("\n🔍 Testing payload consistency between services...")
    
    try:
        # Read both service files
        with open('backend/services/labor_request_service.py', 'r') as f:
            regular_content = f.read()
        
        with open('backend/services/labor_deletion_service.py', 'r') as f:
            negative_content = f.read()
        
        # Check that both use the same API parameters
        api_params = [
            "'lean': '1'",
            "'ignorecollectionref': '1'",
            "'ignorekeyref': '1'",
            "'ignorers': '1'",
            "'mxlaction': 'addchange'"
        ]
        
        for param in api_params:
            if param in regular_content and param in negative_content:
                print(f"✅ API parameter {param} consistent in both services")
            else:
                print(f"❌ API parameter {param} inconsistent between services")
                return False
        
        # Check that both use the same headers
        headers = [
            "'x-method-override': 'BULK'",
            "'Content-Type': 'application/json'",
            "'Accept': 'application/json'"
        ]
        
        for header in headers:
            if header in regular_content and header in negative_content:
                print(f"✅ Header {header} consistent in both services")
            else:
                print(f"❌ Header {header} inconsistent between services")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Error testing payload consistency: {e}")
        return False

def test_api_endpoint_structure():
    """Test that API endpoints are properly structured"""
    print("\n🔍 Testing API endpoint structure...")
    
    try:
        # Read the app.py file
        with open('app.py', 'r') as f:
            app_content = f.read()
        
        # Check for regular labor endpoint
        if '/api/task/<task_wonum>/add-labor' in app_content:
            print("✅ Regular labor endpoint found")
        else:
            print("❌ Regular labor endpoint missing")
            return False
        
        # Check for negative labor endpoint
        if '/api/task/<task_wonum>/add-negative-labor' in app_content:
            print("✅ Negative labor endpoint found")
        else:
            print("❌ Negative labor endpoint missing")
            return False
        
        # Check that regularhrs parameter is extracted in regular labor endpoint
        if 'regularhrs = data.get(\'regularhrs\')' in app_content:
            print("✅ regularhrs parameter extracted in regular labor endpoint")
        else:
            print("❌ regularhrs parameter not extracted in regular labor endpoint")
            return False
        
        # Check that negative_hours parameter is extracted in negative labor endpoint
        if 'negative_hours = data.get(\'negative_hours\')' in app_content:
            print("✅ negative_hours parameter extracted in negative labor endpoint")
        else:
            print("❌ negative_hours parameter not extracted in negative labor endpoint")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing API endpoint structure: {e}")
        return False

def test_frontend_integration():
    """Test that frontend properly sends the required fields"""
    print("\n🔍 Testing frontend integration...")
    
    try:
        # Read the labor_search.js file
        with open('frontend/static/js/labor_search.js', 'r') as f:
            js_content = f.read()
        
        # Check that regular labor submission includes regularhrs
        if 'regularhrs:' in js_content:
            print("✅ regularhrs field found in frontend submission")
        else:
            print("❌ regularhrs field missing from frontend submission")
            return False
        
        # Check that negative hours submission includes negative_hours
        if 'negative_hours:' in js_content:
            print("✅ negative_hours field found in frontend submission")
        else:
            print("❌ negative_hours field missing from frontend submission")
            return False
        
        # Check that negative hours are properly converted
        if 'negativeHours = -Math.abs(' in js_content:
            print("✅ Negative hours conversion found in frontend")
        else:
            print("❌ Negative hours conversion missing from frontend")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing frontend integration: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Labor Payload Fixes")
    print("=" * 60)
    
    tests = [
        ("Regular Labor Payload Structure", test_regular_labor_payload_structure),
        ("Negative Labor Payload Structure", test_negative_labor_payload_structure),
        ("Time Conflict Handling", test_time_conflict_handling),
        ("Payload Consistency", test_payload_consistency),
        ("API Endpoint Structure", test_api_endpoint_structure),
        ("Frontend Integration", test_frontend_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Labor payload fixes are complete.")
        print("\n📋 Fixed Issues:")
        print("✅ Issue 1: Added regularhrs field to regular labor payload")
        print("✅ Issue 2: Fixed negative labor payload structure to match regular labor")
        print("✅ Issue 3: Added time conflict handling and warnings")
        print("✅ Consistency: Both services use identical payload structures")
        print("✅ Frontend: Properly sends required fields for both operations")
        print("\n🔧 Payload Structure (Both Regular and Negative):")
        print("""[
  {
    "_action": "AddChange",
    "wonum": "WORK_ORDER_NUMBER",
    "siteid": "SITE_ID",
    "labtrans": [
      {
        "laborcode": "LABOR_CODE",
        "regularhrs": 8.0,  // Positive for regular, negative for adjustments
        "taskid": 40,
        "genapprservreceipt": 1,
        "startdate": "2025-06-26T00:00:00",  // Optional
        "starttime": "1970-01-01T08:00:00",  // Optional
        "finishtime": "1970-01-01T16:00:00", // Optional
        "transtype": "WORK"
      }
    ]
  }
]""")
        return True
    else:
        print(f"❌ {total - passed} test(s) failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    main()
